"""
CLIP图像嵌入与索引构建模块
使用CLIP模型对图像进行嵌入，构建FAISS索引用于后续检索
"""

import os
import numpy as np
import faiss
import json
from PIL import Image
import torch
from transformers import CLIPModel, CLIPProcessor
from typing import List, Tuple, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CLIPImageIndexer:
    """CLIP图像嵌入与索引构建器"""
    
    def __init__(self, model_path: str = None):
        """
        初始化CLIP图像索引器
        
        Args:
            model_path: CLIP模型路径，默认使用本地模型
        """
        # 设置模型路径
        if model_path is None:
            # 使用本地模型路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_path = os.path.join(current_dir, "../../models/clip-vit-base-patch16")
        
        self.model_path = model_path
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 初始化模型和处理器
        self._load_model()
        
        # 索引相关
        self.index = None
        self.image_paths = []
        self.embeddings = None
        
        # 文件路径
        self.index_dir = os.path.join(os.path.dirname(__file__))
        self.index_file = os.path.join(self.index_dir, "faiss_index.index")
        self.paths_file = os.path.join(self.index_dir, "image_paths.json")
        self.embeddings_file = os.path.join(self.index_dir, "clip_embeddings.npy")
    
    def _load_model(self):
        """加载CLIP模型和处理器"""
        try:
            logger.info(f"Loading CLIP model from {self.model_path}")
            self.model = CLIPModel.from_pretrained(self.model_path)
            self.processor = CLIPProcessor.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            logger.info("CLIP model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load CLIP model: {e}")
            raise
    
    def extract_image_embedding(self, image_path: str) -> np.ndarray:
        """
        提取单张图像的CLIP嵌入
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            图像嵌入向量
        """
        try:
            # 加载和预处理图像
            image = Image.open(image_path).convert('RGB')
            inputs = self.processor(images=image, return_tensors="pt")
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 提取图像特征
            with torch.no_grad():
                image_features = self.model.get_image_features(**inputs)
                # 归一化特征向量
                image_features = image_features / image_features.norm(dim=-1, keepdim=True)
            
            return image_features.cpu().numpy().flatten()
        
        except Exception as e:
            logger.error(f"Failed to extract embedding for {image_path}: {e}")
            return None
    
    def build_index_from_directory(self, image_dir: str, supported_formats: List[str] = None) -> bool:
        """
        从图像目录构建FAISS索引
        
        Args:
            image_dir: 图像目录路径
            supported_formats: 支持的图像格式列表
            
        Returns:
            是否成功构建索引
        """
        if supported_formats is None:
            supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
        logger.info(f"Building index from directory: {image_dir}")
        
        # 收集所有图像文件
        image_files = []
        if os.path.exists(image_dir):
            for root, dirs, files in os.walk(image_dir):
                for file in files:
                    if any(file.lower().endswith(fmt) for fmt in supported_formats):
                        image_files.append(os.path.join(root, file))
        
        if not image_files:
            logger.warning(f"No image files found in {image_dir}")
            return False
        
        logger.info(f"Found {len(image_files)} image files")
        
        # 提取所有图像的嵌入
        embeddings = []
        valid_paths = []
        
        for i, image_path in enumerate(image_files):
            logger.info(f"Processing image {i+1}/{len(image_files)}: {os.path.basename(image_path)}")
            
            embedding = self.extract_image_embedding(image_path)
            if embedding is not None:
                embeddings.append(embedding)
                valid_paths.append(image_path)
        
        if not embeddings:
            logger.error("No valid embeddings extracted")
            return False
        
        # 转换为numpy数组
        embeddings_array = np.vstack(embeddings).astype('float32')
        
        # 构建FAISS索引
        dimension = embeddings_array.shape[1]
        logger.info(f"Building FAISS index with dimension {dimension}")
        
        # 使用内积索引（归一化后等价于余弦相似度）
        self.index = faiss.IndexFlatIP(dimension)
        
        # 添加向量到索引
        self.index.add(embeddings_array)
        
        # 保存数据
        self.embeddings = embeddings_array
        self.image_paths = valid_paths
        
        logger.info(f"Index built successfully with {len(valid_paths)} images")
        return True
    
    def save_index(self) -> bool:
        """
        保存索引到磁盘
        
        Returns:
            是否成功保存
        """
        try:
            if self.index is None:
                logger.error("No index to save")
                return False
            
            # 保存FAISS索引
            faiss.write_index(self.index, self.index_file)
            logger.info(f"FAISS index saved to {self.index_file}")
            
            # 保存图像路径
            with open(self.paths_file, 'w', encoding='utf-8') as f:
                json.dump(self.image_paths, f, ensure_ascii=False, indent=2)
            logger.info(f"Image paths saved to {self.paths_file}")
            
            # 保存嵌入向量
            if self.embeddings is not None:
                np.save(self.embeddings_file, self.embeddings)
                logger.info(f"Embeddings saved to {self.embeddings_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to save index: {e}")
            return False
    
    def load_index(self) -> bool:
        """
        从磁盘加载索引
        
        Returns:
            是否成功加载
        """
        try:
            # 检查文件是否存在
            if not all(os.path.exists(f) for f in [self.index_file, self.paths_file]):
                logger.warning("Index files not found")
                return False
            
            # 加载FAISS索引
            self.index = faiss.read_index(self.index_file)
            logger.info(f"FAISS index loaded from {self.index_file}")
            
            # 加载图像路径
            with open(self.paths_file, 'r', encoding='utf-8') as f:
                self.image_paths = json.load(f)
            logger.info(f"Image paths loaded from {self.paths_file}")
            
            # 加载嵌入向量（可选）
            if os.path.exists(self.embeddings_file):
                self.embeddings = np.load(self.embeddings_file)
                logger.info(f"Embeddings loaded from {self.embeddings_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load index: {e}")
            return False
    
    def get_index_info(self) -> dict:
        """
        获取索引信息
        
        Returns:
            索引信息字典
        """
        if self.index is None:
            return {"status": "No index loaded"}
        
        return {
            "status": "Index loaded",
            "total_images": self.index.ntotal,
            "dimension": self.index.d,
            "index_type": type(self.index).__name__,
            "image_paths_count": len(self.image_paths)
        }


def main():
    """测试函数"""
    # 初始化索引器
    indexer = CLIPImageIndexer()
    
    # 构建索引
    image_dir = "src/ai_workflow/input/image"
    if indexer.build_index_from_directory(image_dir):
        # 保存索引
        indexer.save_index()
        
        # 显示索引信息
        info = indexer.get_index_info()
        print("Index Info:", info)
    else:
        print("Failed to build index")


if __name__ == "__main__":
    main()
