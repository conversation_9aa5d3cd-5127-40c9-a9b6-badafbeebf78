#!/usr/bin/env python3
"""
简化版MusicGen测试脚本
"""

import os
import sys

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_music_generator():
    """测试简化的MusicGenerator"""
    print("=== 测试简化版MusicGenerator ===")
    
    try:
        from music_generator import MusicGenerator, MUSICGEN_AVAILABLE
        
        print(f"MusicGen可用: {MUSICGEN_AVAILABLE}")
        
        # 创建生成器实例
        generator = MusicGenerator()
        print("✓ MusicGenerator实例创建成功")
        
        if MUSICGEN_AVAILABLE:
            # 测试基本音乐生成
            print("\n测试基本音乐生成...")
            result = generator.generate_music(
                "peaceful background music",
                "test_basic",
                "common"
            )
            print(f"基本生成结果: {result}")
            
            # 测试节点音乐生成
            print("\n测试节点音乐生成...")
            test_narrative = {
                "node_id": "test_node_001",
                "narrative_content": {
                    "music_instructions": [
                        "peaceful piano music for introduction",
                        "sad violin music for emotional scene"
                    ]
                }
            }
            
            node_result = generator.generate_music_for_node(test_narrative)
            print(f"节点生成结果: {node_result}")
            
            # 测试默认音乐生成
            print("\n测试默认音乐生成...")
            default_narrative = {
                "node_id": "test_node_002",
                "narrative_content": {}
            }
            
            default_result = generator.generate_music_for_node(default_narrative)
            print(f"默认生成结果: {default_result}")
            
        else:
            print("MusicGen依赖不可用，跳过实际生成测试")
            
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\n=== 测试向后兼容性 ===")
    
    try:
        from music_generator import generate_music
        
        result = generate_music(
            "test music",
            "test_compat",
            "common"
        )
        print(f"向后兼容函数结果: {result}")
        return True
        
    except Exception as e:
        print(f"✗ 向后兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("简化版MusicGen测试开始")
    print("=" * 50)
    
    success = True
    
    # 测试MusicGenerator类
    if not test_music_generator():
        success = False
    
    # 测试向后兼容性
    if not test_backward_compatibility():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✓ 所有测试通过！")
    else:
        print("✗ 部分测试失败")
    
    print("\n使用说明:")
    print("1. 如果MusicGen依赖不可用，请运行: python install_musicgen_deps.py")
    print("2. 或手动安装: pip install torch transformers scipy")
    print("3. 推荐使用 facebook/musicgen-small 模型进行测试")

if __name__ == "__main__":
    main()
