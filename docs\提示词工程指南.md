# 🎯 AI-GAL 提示词工程指南

## 📁 提示词库结构

AI-GAL系统将所有提示词集中管理在 `prompts/` 目录下，便于进行提示词工程优化：

```
prompts/
├── __init__.py                 # 模块初始化
├── framework_prompts.py        # 游戏框架生成提示词
├── narrative_prompts.py        # 节点叙事生成提示词
├── image_prompts.py           # 图像生成提示词
└── audio_prompts.py           # 音频生成提示词
```

## 🔧 提示词模块详解

### 1. 游戏框架生成提示词 (`framework_prompts.py`)

**用途**: 控制游戏整体结构和角色设定的生成

**关键提示词**:
- `story_skeleton`: 剧情骨架生成
- `character_settings`: 角色设定生成

**优化要点**:
- 强调文化遗产主题的准确性
- 确保剧情节点的结构化输出
- 平衡教育性与娱乐性

### 2. 节点叙事生成提示词 (`narrative_prompts.py`)

**用途**: 控制每个节点的详细剧本生成

**关键提示词**:
- `node_narrative`: 节点叙事生成
- `summary_extraction`: 文本摘要生成
- `full_summary`: 整体摘要生成

**优化要点**:
- 标记化输出的准确性 ([Setting:], [Music:]等)
- 对话的自然性和角色一致性
- 文化内容的准确传达

### 3. 图像生成提示词 (`image_prompts.py`)

**用途**: 控制Stable Diffusion图像生成的提示词

**关键提示词**:
- `image_prompt_generation`: 通用图像提示词生成
- `background_prompt`: 背景图像专用
- `character_prompt`: 角色立绘专用
- `cg_prompt`: CG插图专用

**优化要点**:
- 英文关键词的准确性
- 正向/负向提示词的平衡
- 文化元素的视觉表达

### 4. 音频生成提示词 (`audio_prompts.py`)

**用途**: 控制音乐和语音生成的描述

**关键提示词**:
- `music_description`: 音乐描述生成
- `background_music`: 背景音乐设计
- `voice_emotion`: 语音情感分析
- `sound_effect`: 音效设计

**优化要点**:
- 音乐风格的准确描述
- 情感表达的细腻度
- 文化氛围的音频体现

## 🎨 提示词优化策略

### 1. 结构化设计原则

**系统提示词结构**:
```python
"system": """你是一名[专业角色]，使用[技术方法]进行[具体任务]。

任务要求：
1. [具体要求1]
2. [具体要求2]
...

输出格式：
[明确的格式要求]

注意：
[重要注意事项]"""
```

**用户提示词结构**:
```python
"user": """请基于以下信息[执行任务]：

[输入信息]：{variable1}
[参考资料]：{variable2}

要求：
1. [具体要求1]
2. [具体要求2]
..."""
```

### 2. 文化遗产专用优化

**文化准确性**:
- 强调基于真实文化资料
- 要求引用具体文化元素
- 避免文化误解和刻板印象

**教育价值**:
- 平衡娱乐性与教育性
- 突出文化传承意义
- 鼓励深度文化探索

### 3. 技术适配优化

**模型适配**:
- 针对Deepseek-R1的推理特点
- 利用模型的文化知识优势
- 适配中英文混合输出需求

**输出控制**:
- 明确的格式要求
- 结构化的标记系统
- 可解析的输出格式

## 🔄 提示词迭代流程

### 1. 基线建立
- 使用通用提示词建立基线效果
- 记录初始生成质量指标
- 识别主要问题点

### 2. 针对性优化
- 分析具体问题类型
- 调整相应提示词部分
- 进行A/B测试对比

### 3. 效果验证
- 使用标准测试用例
- 评估生成内容质量
- 收集用户反馈

### 4. 持续改进
- 定期回顾提示词效果
- 根据新需求调整
- 保持版本控制

## 📊 提示词评估指标

### 1. 内容质量指标
- **准确性**: 文化内容的准确程度
- **连贯性**: 生成内容的逻辑连贯性
- **创新性**: 内容的创意和新颖度
- **完整性**: 输出格式的完整程度

### 2. 技术指标
- **解析成功率**: 输出格式的可解析性
- **标记准确率**: 标记化输出的准确性
- **生成稳定性**: 多次生成的一致性
- **处理效率**: 生成速度和资源消耗

### 3. 用户体验指标
- **可读性**: 生成内容的可读性
- **沉浸感**: 游戏体验的沉浸程度
- **教育效果**: 文化知识的传达效果
- **满意度**: 用户整体满意度

## 🛠️ 提示词调试技巧

### 1. 分层调试
```python
# 1. 测试系统提示词
test_system_prompt_only()

# 2. 测试用户提示词模板
test_user_prompt_template()

# 3. 测试完整提示词组合
test_full_prompt_combination()
```

### 2. 变量控制
- 固定其他变量，单独测试目标提示词
- 使用标准测试数据集
- 记录每次修改的具体效果

### 3. 输出分析
- 分析生成内容的结构
- 检查关键信息的提取
- 验证格式标记的正确性

## 📝 提示词维护规范

### 1. 版本控制
- 每次修改记录版本号
- 保留历史版本备份
- 记录修改原因和效果

### 2. 文档更新
- 及时更新提示词说明
- 记录最佳实践案例
- 维护问题解决方案库

### 3. 团队协作
- 建立提示词审核流程
- 统一提示词编写规范
- 定期进行效果评估会议

## 🎯 高级优化技巧

### 1. 上下文工程
- 利用RAG检索增强上下文
- 动态调整上下文长度
- 优化上下文信息的组织

### 2. 少样本学习
- 在提示词中提供优质示例
- 使用思维链(Chain-of-Thought)技术
- 引导模型进行分步推理

### 3. 多轮对话优化
- 设计多轮交互流程
- 利用历史对话信息
- 实现渐进式内容完善

---

通过系统化的提示词工程，AI-GAL能够生成更高质量、更符合文化遗产主题的游戏内容。持续的优化和迭代是提升系统效果的关键。
