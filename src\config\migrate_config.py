#!/usr/bin/env python3
"""
配置迁移脚本
将旧的config.ini配置迁移到新的JSON配置系统
"""

import os
import json
import configparser
from pathlib import Path

def migrate_config():
    """迁移配置文件"""
    
    # 获取项目根目录
    project_root = Path(__file__).parent.parent.parent
    old_config_path = project_root / "config.ini"
    
    if not old_config_path.exists():
        print("未找到config.ini文件，跳过迁移")
        return
    
    print("开始迁移配置文件...")
    
    # 读取旧配置
    config = configparser.ConfigParser()
    config.read(old_config_path, encoding='utf-8')
    
    # 迁移API配置
    api_config = {
        "openai": {
            "api_key": config.get('CHATGPT', 'gpt_key', fallback=''),
            "base_url": config.get('CHATGPT', 'base_url', fallback='https://api.deepseek.com/v1'),
            "model": config.get('CHATGPT', 'model', fallback='deepseek-reasoner'),
            "temperature": 0.7,
            "max_tokens": 4000,
            "timeout": 120,
            "retry_times": 3,
            "retry_delay": 1
        },
        "dashscope": {
            "api_key": "",
            "base_url": "https://dashscope.aliyuncs.com/api/v1",
            "model": "qwen-turbo",
            "temperature": 0.7,
            "max_tokens": 4000,
            "timeout": 120,
            "retry_times": 3,
            "retry_delay": 1
        },
        "custom": {
            "api_key": "",
            "base_url": "",
            "model": "",
            "temperature": 0.7,
            "max_tokens": 4000,
            "timeout": 120,
            "retry_times": 3,
            "retry_delay": 1
        },
        "default_provider": "openai",
        "fallback_providers": ["dashscope", "custom"],
        "enable_fallback": True,
        "rate_limiting": {
            "requests_per_minute": 60,
            "tokens_per_minute": 100000,
            "enable_rate_limit": False
        },
        "caching": {
            "enable_cache": True,
            "cache_ttl": 3600,
            "max_cache_size": 1000
        }
    }
    
    # 迁移生成器配置
    generators_config = {
        "image_generation": {
            "use_cloud": config.getboolean('AI绘画', 'if_cloud', fallback=False),
            "cloud_api_key": config.get('AI绘画', 'draw_key', fallback=''),
            "cloud_base_url": config.get('AI绘画', 'base_url', fallback=''),
            "local_url": "http://127.0.0.1:7860",
            "output_directory": "images",
            "default_style": "realistic",
            "image_size": "512x512",
            "batch_size": 1,
            "timeout": 300
        },
        "vocal_generation": {
            "use_cloud": config.getboolean('SOVITS', 'if_cloud', fallback=False),
            "cloud_api_key": config.get('SOVITS', '语音key', fallback=''),
            "cloud_base_url": config.get('SOVITS', 'base_url', fallback=''),
            "local_url": "http://127.0.0.1:9880",
            "sovits_version": "V2" if config.get('SOVITS', 'version', fallback='0') == "1" else "V1",
            "output_directory": "audio",
            "default_speaker": 1,
            "audio_format": "wav",
            "sample_rate": 22050,
            "timeout": 180
        },
        "music_generation": {
            "enabled": config.getboolean('AI音乐', 'if_on', fallback=False),
            "use_cloud": config.getboolean('AI音乐', 'if_cloud', fallback=False),
            "cloud_api_key": config.get('AI音乐', 'api_key', fallback=''),
            "cloud_base_url": config.get('AI音乐', 'base_url', fallback=''),
            "local_url": config.get('AI音乐', 'local_url', fallback='http://127.0.0.1:7861'),
            "local_model_path": config.get('AI音乐', 'local_model_path', fallback=''),
            "output_directory": config.get('AI音乐', 'local_output_dir', fallback='music'),
            "default_style": "common",
            "audio_format": "mp3",
            "timeout": 600,
            "styles": {
                "sad": "Pure music, light music, game, galgame, piano, sad, violin, melancholy",
                "common": "Pure music, light music, relaxed, cafe, game, galgame, piano, peaceful",
                "epic": "Epic, orchestral, cinematic, dramatic, game, adventure, heroic",
                "ambient": "Ambient, atmospheric, peaceful, meditation, background, soft",
                "upbeat": "Upbeat, energetic, positive, cheerful, game, electronic, happy"
            }
        },
        "narrative_generation": {
            "model": "deepseek-reasoner",
            "temperature": 0.7,
            "max_tokens": 4000,
            "timeout": 120,
            "output_directory": "narratives",
            "enable_rag": True,
            "rag_top_k": 3,
            "enable_character_consistency": True
        },
        "framework_generation": {
            "model": "deepseek-reasoner",
            "temperature": 0.8,
            "max_tokens": 6000,
            "timeout": 180,
            "output_directory": "frameworks",
            "enable_rag": True,
            "rag_top_k": 5,
            "default_node_count": 8,
            "enable_branching": True
        },
        "renpy_integration": {
            "output_directory": "renpy_game",
            "script_filename": "script.rpy",
            "enable_gui_generation": True,
            "enable_options_generation": True,
            "character_prefix": "define",
            "label_prefix": "label"
        },
        "ui_design": {
            "output_directory": "ui",
            "theme": "cultural_heritage",
            "enable_responsive": True,
            "color_scheme": "traditional",
            "font_family": "default"
        }
    }
    
    # 保存配置文件
    config_dir = Path(__file__).parent
    
    # 保存API配置
    with open(config_dir / "config_api.json", 'w', encoding='utf-8') as f:
        json.dump(api_config, f, indent=2, ensure_ascii=False)
    
    # 保存生成器配置
    with open(config_dir / "config_generators.json", 'w', encoding='utf-8') as f:
        json.dump(generators_config, f, indent=2, ensure_ascii=False)
    
    print("配置迁移完成！")
    print("新配置文件已保存到:")
    print(f"  - {config_dir / 'config_api.json'}")
    print(f"  - {config_dir / 'config_generators.json'}")
    print(f"  - {config_dir / 'config_rag.json'}")
    print(f"  - {config_dir / 'config_pipeline.json'}")
    print(f"  - {config_dir / 'config_preprocessor.json'}")
    
    print("\n请检查配置文件并根据需要调整参数。")

if __name__ == "__main__":
    migrate_config()
