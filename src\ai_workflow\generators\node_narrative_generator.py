"""
节点叙事生成模块 (Deepseek-R1 Phase II)
为每个主线节点生成具体的叙事内容、对话和场景说明
"""

import os
import json
import re
from typing import List, Dict
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section
from src.ai_workflow.utils.gpt_client import gpt
from src.ai_workflow.rag.rag_retrieval import RAGRetrieval

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

class NodeNarrativeGenerator:
    """节点叙事生成器类"""

    def __init__(self):
        """初始化节点叙事生成器"""
        # 获取叙事生成配置
        self.narrative_config = get_section('generators', 'narrative_generation')

        self.rag = RAGRetrieval()

        # 输出目录
        output_dir = self.narrative_config.get('output_directory', 'narratives')
        self.narratives_dir = os.path.join(game_directory, "data", output_dir)
        os.makedirs(self.narratives_dir, exist_ok=True)

        # 从提示词库加载提示词
        from src.ai_workflow.generators.prompts.narrative_prompts import NARRATIVE_GENERATION_PROMPTS
        self.prompts = NARRATIVE_GENERATION_PROMPTS

    def generate_all_node_narratives(self, framework: Dict) -> List[Dict]:
        """
        为框架中的所有节点生成叙事内容

        Args:
            framework: 游戏框架数据

        Returns:
            所有节点的叙事内容列表
        """
        print("开始生成节点叙事内容 (Phase II)...")

        all_narratives = []
        story_skeleton = framework.get("story_skeleton", {})
        characters = framework.get("characters", [])

        # 处理主线节点
        mainline_nodes = story_skeleton.get("mainline_nodes", [])
        for i, node in enumerate(mainline_nodes):
            print(f"生成主线节点 {node['node_id']} 的叙事内容...")

            # 获取前文情节（前一个节点的简要描述）
            previous_plot = ""
            if i > 0:
                prev_node = mainline_nodes[i-1]
                previous_plot = f"节点 {prev_node['node_id']}: {prev_node['description']}"

            # 生成节点叙事
            node_narrative = self.generate_node_narrative(
                node,
                characters,
                previous_plot,
                framework.get("reference_context", {})
            )

            all_narratives.append(node_narrative)

            # 保存单个节点叙事
            self.save_node_narrative(node_narrative)

        # 处理支线节点
        sideline_nodes = story_skeleton.get("sideline_nodes", [])
        for node in sideline_nodes:
            print(f"生成支线节点 {node['node_id']} 的叙事内容...")

            # 查找关联的主线节点作为前文
            related_mainline = self._find_related_mainline(node, mainline_nodes)

            # 生成节点叙事
            node_narrative = self.generate_node_narrative(
                node,
                characters,
                related_mainline,
                framework.get("reference_context", {})
            )

            all_narratives.append(node_narrative)

            # 保存单个节点叙事
            self.save_node_narrative(node_narrative)

        # 保存所有节点叙事
        self.save_all_narratives(all_narratives)

        print(f"节点叙事生成完成，共 {len(all_narratives)} 个节点")
        return all_narratives

    def generate_node_narrative(self, node: Dict, characters: List[Dict],
                              previous_plot: str, reference_context: Dict) -> Dict:
        """
        为单个节点生成叙事内容

        Args:
            node: 节点数据
            characters: 角色设定列表
            previous_plot: 前文情节
            reference_context: 参考上下文

        Returns:
            节点叙事数据
        """
        node_id = node.get("node_id", "unknown")
        description = node.get("description", "")
        key_events = node.get("key_events", [])

        # 获取节点相关的参考资料
        references = self._get_relevant_references(node, reference_context)

        # 获取节点相关的角色信息
        node_characters = self._get_relevant_characters(node, characters)

        # 构建提示词参数
        characters_text = self._format_characters_text(node_characters)
        references_text = self._format_references_text(references)
        key_events_text = "\n".join(key_events) if key_events else "无特定关键事件"

        # 使用GPT生成叙事内容
        system_prompt = self.prompts["node_narrative"]["system"]
        user_prompt = self.prompts["node_narrative"]["user"].format(
            node_id=node_id,
            description=description,
            key_events=key_events_text,
            characters_text=characters_text,
            previous_plot=previous_plot,
            references_text=references_text
        )

        try:
            narrative_response = gpt(system_prompt, user_prompt)
            parsed_narrative = self._parse_narrative_response(narrative_response)

            # 构建完整的节点叙事数据
            node_narrative = {
                "node_id": node_id,
                "node_description": description,
                "narrative_content": parsed_narrative,
                "generation_metadata": {
                    "phase": "Phase II - Narrative Generation",
                    "model": "Deepseek-R1",
                    "timestamp": self._get_current_time()
                }
            }

            return node_narrative

        except Exception as e:
            print(f"节点 {node_id} 叙事生成失败: {e}")
            return self._generate_default_narrative(node)

    def _parse_narrative_response(self, response: str) -> Dict:
        """
        解析生成的叙事响应

        Args:
            response: 生成的叙事文本

        Returns:
            结构化的叙事内容
        """
        narrative = {
            "settings": [],
            "dialogues": [],
            "narrations": [],
            "music_instructions": [],
            "sound_effects": [],
            "cg_scenes": [],
            "characters": [],
            "raw_text": response
        }

        # 提取场景设置 [Setting: ...]
        settings = re.findall(r'\[Setting:([^\]]+)\]', response)
        narrative["settings"] = [setting.strip() for setting in settings]

        # 提取音乐指令 [Music: ...]
        music_instructions = re.findall(r'\[Music:([^\]]+)\]', response)
        narrative["music_instructions"] = [music.strip() for music in music_instructions]

        # 提取音效 [Sound: ...]
        sound_effects = re.findall(r'\[Sound:([^\]]+)\]', response)
        narrative["sound_effects"] = [sound.strip() for sound in sound_effects]

        # 提取特殊CG场景 [Image: ...]
        cg_scenes = re.findall(r'\[Image:([^\]]+)\]', response)
        narrative["cg_scenes"] = [cg.strip() for cg in cg_scenes]

        # 提取对话
        # 格式: 角色名(情感/动作)："对话内容"
        dialogues = re.findall(r'([^"\n]+?)(?:\(([^)]+)\))?:\s*"([^"]+)"', response)
        for dialogue in dialogues:
            character = dialogue[0].strip()
            emotion = dialogue[1].strip() if len(dialogue) > 1 else ""
            content = dialogue[2].strip()

            narrative["dialogues"].append({
                "character": character,
                "emotion": emotion,
                "content": content
            })

            # 收集角色信息
            if character not in [char["name"] for char in narrative["characters"]]:
                narrative["characters"].append({
                    "name": character,
                    "description": ""  # 可以在后续处理中补充
                })

        # 提取旁白（非对话文本）
        lines = response.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('[') and '"' not in line and ':' not in line:
                narrative["narrations"].append(line)

        return narrative

    def _get_relevant_references(self, node: Dict, reference_context: Dict) -> List[Dict]:
        """获取节点相关的参考资料"""
        references = []

        # 从节点描述中提取关键词
        description = node.get("description", "")

        # 使用RAG检索相关内容
        sad_results = self.rag.search_sad(description, top_k=3)
        for result in sad_results:
            references.append({
                "content": result["document"],
                "source": result["metadata"].get("material_name", ""),
                "type": "text",
                "similarity": result["similarity"]
            })

        # 检索相关图像描述
        vsi_results = self.rag.search_vsi(description, top_k=2)
        for result in vsi_results:
            references.append({
                "content": result["document"],
                "source": result["metadata"].get("image_path", ""),
                "type": "image",
                "similarity": result["similarity"]
            })

        return references

    def _get_relevant_characters(self, node: Dict, all_characters: List[Dict]) -> List[Dict]:
        """获取节点相关的角色信息"""
        relevant_characters = []

        # 从节点中提取角色信息
        node_character_names = []
        for char_info in node.get("characters", []):
            if ":" in char_info:
                char_name = char_info.split(":")[0].strip()
                node_character_names.append(char_name)

        # 匹配完整的角色信息
        for character in all_characters:
            if character["name"] in node_character_names:
                relevant_characters.append(character)

        return relevant_characters

    def _format_characters_text(self, characters: List[Dict]) -> str:
        """格式化角色信息文本"""
        if not characters:
            return "无特定角色信息"

        text_parts = []
        for character in characters:
            char_text = f"{character['name']}: {character['description']}"
            if character.get("details"):
                details = "\n".join([f"- {detail}" for detail in character["details"]])
                char_text += f"\n{details}"
            text_parts.append(char_text)

        return "\n\n".join(text_parts)

    def _format_references_text(self, references: List[Dict]) -> str:
        """格式化参考资料文本"""
        if not references:
            return "无相关参考资料"

        text_parts = []
        for ref in references:
            ref_type = ref.get("type", "text")
            source = ref.get("source", "未知来源")
            content = ref.get("content", "")

            text_parts.append(f"[{ref_type}] {source}: {content}")

        return "\n\n".join(text_parts)

    def _find_related_mainline(self, sideline_node: Dict, mainline_nodes: List[Dict]) -> str:
        """查找支线节点关联的主线节点"""
        # 简单实现：假设支线节点ID格式为"B1"，对应主线节点"A1"
        sideline_id = sideline_node.get("node_id", "")
        if sideline_id.startswith("B"):
            mainline_id = "A" + sideline_id[1:]

            for node in mainline_nodes:
                if node.get("node_id") == mainline_id:
                    return f"主线节点 {mainline_id}: {node.get('description', '')}"

        return "无关联主线节点"

    def _generate_default_narrative(self, node: Dict) -> Dict:
        """生成默认的节点叙事"""
        node_id = node.get("node_id", "unknown")
        description = node.get("description", "")

        return {
            "node_id": node_id,
            "node_description": description,
            "narrative_content": {
                "settings": ["默认场景"],
                "dialogues": [
                    {"character": "主角", "emotion": "平静", "content": "这是一个默认生成的对话。"}
                ],
                "narrations": ["这是默认生成的旁白内容。"],
                "music_instructions": ["默认背景音乐"],
                "sound_effects": [],
                "cg_scenes": [],
                "characters": [{"name": "主角", "description": ""}],
                "raw_text": f"默认生成的节点 {node_id} 叙事内容。"
            },
            "generation_metadata": {
                "phase": "Phase II - Default Narrative",
                "timestamp": self._get_current_time()
            }
        }

    def save_node_narrative(self, narrative: Dict) -> str:
        """保存单个节点叙事"""
        node_id = narrative.get("node_id", "unknown")
        filename = f"narrative_{node_id}.json"
        filepath = os.path.join(self.narratives_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(narrative, f, ensure_ascii=False, indent=2)

        return filepath

    def save_all_narratives(self, narratives: List[Dict]) -> str:
        """保存所有节点叙事"""
        timestamp = self._get_current_time().replace(':', '-').replace(' ', '_')
        filename = f"all_narratives_{timestamp}.json"
        filepath = os.path.join(self.narratives_dir, filename)

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(narratives, f, ensure_ascii=False, indent=2)

        # 同时保存为最新版本
        latest_path = os.path.join(self.narratives_dir, "latest_narratives.json")
        with open(latest_path, 'w', encoding='utf-8') as f:
            json.dump(narratives, f, ensure_ascii=False, indent=2)

        return filepath

    def load_latest_narratives(self) -> List[Dict]:
        """加载最新的节点叙事"""
        latest_path = os.path.join(self.narratives_dir, "latest_narratives.json")
        if os.path.exists(latest_path):
            with open(latest_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return []

    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")