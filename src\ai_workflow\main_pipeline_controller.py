"""
主流程控制器
整合所有模块，实现从素材输入到游戏输出的完整流程控制
"""

import os
import json
import time
from typing import List, Dict
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section

from src.ai_workflow.preprocess.material_preprocessor import MaterialPreprocessor
from src.ai_workflow.rag.rag_retrieval import RAGRetrieval
from src.ai_workflow.generators.game_framework_generator import GameFrameworkGenerator
from src.ai_workflow.generators.node_narrative_generator import NodeNarrativeGenerator
from src.ai_workflow.generators.image_generator import ImageGenerator
from src.ai_workflow.generators.vocal_generator import VocalGenerator
from src.ai_workflow.generators.renpy_integrator import RenpyIntegrator
from src.ai_workflow.generators.ui_design_module import UIDesignModule
from src.ai_workflow.generators.music_generator import MusicGenerator

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

class MainPipelineController:
    def __init__(self):
        """初始化主流程控制器"""
        # 获取流程配置
        self.pipeline_config = get_section('pipeline', 'workflow')

        # 输出目录配置
        output_config = get_section('pipeline', 'output')
        base_dir = output_config.get('base_directory', 'output')
        self.output_dir = os.path.join(game_directory, base_dir, "generated_game")
        os.makedirs(self.output_dir, exist_ok=True)

        # 初始化所有模块
        self.material_preprocessor = MaterialPreprocessor()
        self.rag_retrieval = RAGRetrieval()
        self.framework_generator = GameFrameworkGenerator()
        self.narrative_generator = NodeNarrativeGenerator()
        self.image_generator = ImageGenerator()
        self.vocal_generator = VocalGenerator()
        self.music_generator = MusicGenerator()
        self.renpy_integrator = RenpyIntegrator()
        self.ui_module = UIDesignModule()

        # 流程步骤配置
        self.total_steps = self.pipeline_config.get('total_steps', 9)
        self.step_names = self.pipeline_config.get('step_names', [])
        self.current_step = 0
        
        # 流程状态跟踪
        self.pipeline_state = {
            "current_step": 0,
            "total_steps": 9,
            "step_names": [
                "素材预处理",
                "RAG索引构建",
                "游戏框架生成",
                "节点叙事生成",
                "图像生成",
                "音频生成",
                "Ren'Py脚本整合",
                "UI设计生成",
                "最终输出"
            ],
            "start_time": None,
            "step_times": {}
        }

    def generate_complete_game(self, material_paths: List[str], theme: str = None, 
                             reference_materials: List[str] = None) -> Dict:
        """
        完整的游戏生成流程
        从素材输入到游戏输出的一站式处理
        """
        print("=" * 60)
        print("开始文化遗产严肃游戏生成流程")
        print("=" * 60)
        
        self.pipeline_state["start_time"] = time.time()
        results = {}
        
        try:
            # 步骤1: 素材预处理
            results["preprocessing"] = self._step_1_material_preprocessing(material_paths)
            
            # 步骤2: RAG索引构建
            results["rag_indexing"] = self._step_2_rag_indexing()
            
            # 步骤3: 游戏框架生成
            results["framework"] = self._step_3_framework_generation(theme, reference_materials)
            
            # 步骤4: 节点叙事生成
            results["narratives"] = self._step_4_narrative_generation(results["framework"])
            
            # 步骤5: 图像生成
            results["images"] = self._step_5_image_generation(results["narratives"])
            
            # 步骤6: 音频生成
            results["audio"] = self._step_6_audio_generation(results["narratives"])
            
            # 步骤7: Ren'Py脚本整合
            results["renpy_script"] = self._step_7_renpy_integration(
                results["framework"], 
                results["narratives"], 
                results["images"], 
                results["audio"]
            )
            
            # 步骤8: UI设计生成
            results["ui_system"] = self._step_8_ui_generation(
                results["framework"], 
                results["narratives"]
            )
            
            # 步骤9: 最终输出
            results["final_output"] = self._step_9_final_output(results)
            
            # 生成完成报告
            completion_report = self._generate_completion_report(results)
            results["completion_report"] = completion_report
            
            print("=" * 60)
            print("游戏生成流程完成！")
            print("=" * 60)
            
            return results
            
        except Exception as e:
            print(f"流程执行失败: {e}")
            self._save_error_report(e, results)
            raise

    def _step_1_material_preprocessing(self, material_paths: List[str]) -> Dict:
        """步骤1: 素材预处理"""
        self._start_step("素材预处理")
        
        print("处理输入素材...")
        preprocessing_results = self.material_preprocessor.process_materials(material_paths)
        
        print(f"生成SAD文件: {len(preprocessing_results['sad_files'])}")
        print(f"生成VSI文件: {len(preprocessing_results['vsi_files'])}")
        
        self._complete_step()
        return preprocessing_results

    def _step_2_rag_indexing(self) -> Dict:
        """步骤2: RAG索引构建"""
        self._start_step("RAG索引构建")
        
        print("构建检索增强生成索引...")
        self.rag_retrieval.rebuild_index()
        
        stats = self.rag_retrieval.get_statistics()
        print(f"索引文档数量: {stats['total_documents']}")
        print(f"SAD文档: {stats['sad_documents']}, VSI文档: {stats['vsi_documents']}")
        
        self._complete_step()
        return stats

    def _step_3_framework_generation(self, theme: str, reference_materials: List[str]) -> Dict:
        """步骤3: 游戏框架生成"""
        self._start_step("游戏框架生成")
        
        print("生成游戏主线框架和角色设定...")
        framework = self.framework_generator.generate_story_framework(theme, reference_materials)
        
        mainline_count = len(framework['story_skeleton']['mainline_nodes'])
        character_count = len(framework['characters'])
        print(f"生成主线节点: {mainline_count}")
        print(f"生成角色: {character_count}")
        
        self._complete_step()
        return framework

    def _step_4_narrative_generation(self, framework: Dict) -> List[Dict]:
        """步骤4: 节点叙事生成"""
        self._start_step("节点叙事生成")
        
        print("为每个节点生成详细叙事...")
        narratives = self.narrative_generator.generate_all_node_narratives(framework)
        
        total_dialogues = sum(len(n.get("narrative_content", {}).get("dialogues", [])) for n in narratives)
        print(f"生成节点叙事: {len(narratives)}")
        print(f"总对话数量: {total_dialogues}")
        
        self._complete_step()
        return narratives

    def _step_5_image_generation(self, narratives: List[Dict]) -> List[Dict]:
        """步骤5: 图像生成"""
        self._start_step("图像生成")
        
        print("生成游戏所需图像...")
        image_results = []
        
        for narrative in narratives:
            node_images = self.image_generator.generate_images_for_node(narrative)
            image_results.append(node_images)
        
        total_images = sum(
            len(result.get("backgrounds", [])) + 
            len(result.get("characters", [])) + 
            len(result.get("cg_images", []))
            for result in image_results
        )
        print(f"生成图像总数: {total_images}")
        
        self._complete_step()
        return image_results

    def _step_6_audio_generation(self, narratives: List[Dict]) -> List[Dict]:
        """步骤6: 音频生成"""
        self._start_step("音频生成")

        print("生成游戏音频...")
        audio_results = []

        for narrative in narratives:
            # 使用统一的语音生成器生成对话语音
            node_audio = self.vocal_generator.generate_audio_for_node(narrative)

            # 使用统一的音乐生成器生成背景音乐
            node_music = self.music_generator.generate_music_for_node(narrative)

            # 合并音频结果
            combined_audio = {
                "node_id": narrative.get("node_id"),
                "voice_files": node_audio.get("voice_files", []),
                "music_files": node_music.get("music_files", []),
                "sound_files": node_audio.get("sound_files", []),
                "generation_status": "success" if (
                    node_audio.get("generation_status") == "success" and
                    node_music.get("generation_status") == "success"
                ) else "partial"
            }

            audio_results.append(combined_audio)

        total_audio = sum(
            len(result.get("music_files", [])) +
            len(result.get("voice_files", [])) +
            len(result.get("sound_files", []))
            for result in audio_results
        )
        print(f"生成音频文件总数: {total_audio}")

        self._complete_step()
        return audio_results

    def _step_7_renpy_integration(self, framework: Dict, narratives: List[Dict], 
                                image_results: List[Dict], audio_results: List[Dict]) -> str:
        """步骤7: Ren'Py脚本整合"""
        self._start_step("Ren'Py脚本整合")
        
        print("整合Ren'Py游戏脚本...")
        script_path = self.renpy_integrator.integrate_full_game(
            framework, narratives, image_results, audio_results
        )
        
        # 生成配置文件
        self.renpy_integrator.generate_options_file(framework)
        self.renpy_integrator.generate_gui_file()
        
        print(f"Ren'Py脚本生成完成: {script_path}")
        
        self._complete_step()
        return script_path

    def _step_8_ui_generation(self, framework: Dict, narratives: List[Dict]) -> Dict:
        """步骤8: UI设计生成"""
        self._start_step("UI设计生成")
        
        print("生成游戏UI系统...")
        ui_components = self.ui_module.generate_ui_system(framework, narratives)
        
        print(f"UI组件生成完成: {len(ui_components)}")
        
        self._complete_step()
        return ui_components

    def _step_9_final_output(self, results: Dict) -> Dict:
        """步骤9: 最终输出"""
        self._start_step("最终输出")
        
        print("生成最终游戏包...")
        
        # 创建游戏目录结构
        game_structure = self._create_game_structure()
        
        # 复制资源文件
        self._copy_game_resources(results)
        
        # 生成游戏信息文件
        game_info = self._generate_game_info(results)
        
        # 生成安装说明
        installation_guide = self._generate_installation_guide()
        
        final_output = {
            "game_directory": self.output_dir,
            "game_structure": game_structure,
            "game_info": game_info,
            "installation_guide": installation_guide,
            "total_generation_time": time.time() - self.pipeline_state["start_time"]
        }
        
        print(f"游戏生成完成，输出目录: {self.output_dir}")
        print(f"总耗时: {final_output['total_generation_time']:.2f}秒")
        
        self._complete_step()
        return final_output

    def _start_step(self, step_name: str):
        """开始执行步骤"""
        self.pipeline_state["current_step"] += 1
        current = self.pipeline_state["current_step"]
        total = self.pipeline_state["total_steps"]
        
        print(f"\n[{current}/{total}] {step_name}")
        print("-" * 40)
        
        self.pipeline_state["step_times"][step_name] = time.time()

    def _complete_step(self):
        """完成当前步骤"""
        current_step = self.pipeline_state["current_step"]
        step_name = self.pipeline_state["step_names"][current_step - 1]
        
        elapsed = time.time() - self.pipeline_state["step_times"][step_name]
        print(f"✓ {step_name} 完成 (耗时: {elapsed:.2f}秒)")

    def _create_game_structure(self) -> Dict:
        """创建游戏目录结构"""
        structure = {
            "game": os.path.join(self.output_dir, "game"),
            "images": os.path.join(self.output_dir, "game", "images"),
            "audio": os.path.join(self.output_dir, "game", "audio"),
            "fonts": os.path.join(self.output_dir, "game", "fonts"),
            "gui": os.path.join(self.output_dir, "game", "gui")
        }
        
        for dir_path in structure.values():
            os.makedirs(dir_path, exist_ok=True)
        
        return structure

    def _copy_game_resources(self, results: Dict):
        """复制游戏资源"""
        # 这里实现资源文件的复制逻辑
        # 包括图像、音频、脚本文件等
        pass

    def _generate_game_info(self, results: Dict) -> Dict:
        """生成游戏信息"""
        framework = results.get("framework", {})
        narratives = results.get("narratives", [])
        
        game_info = {
            "title": "文化遗产探索游戏",
            "theme": framework.get("theme", "文化遗产"),
            "version": "1.0.0",
            "generated_time": time.strftime("%Y-%m-%d %H:%M:%S"),
            "statistics": {
                "total_nodes": len(narratives),
                "total_characters": len(framework.get("characters", [])),
                "total_dialogues": sum(len(n.get("narrative_content", {}).get("dialogues", [])) for n in narratives),
                "generation_time": time.time() - self.pipeline_state["start_time"]
            }
        }
        
        # 保存游戏信息
        info_path = os.path.join(self.output_dir, "game_info.json")
        with open(info_path, 'w', encoding='utf-8') as f:
            json.dump(game_info, f, ensure_ascii=False, indent=2)
        
        return game_info

    def _generate_installation_guide(self) -> str:
        """生成安装说明"""
        guide = """
# 文化遗产探索游戏 - 安装说明

## 系统要求
- 操作系统: Windows 7/8/10/11, macOS 10.12+, Linux
- 内存: 至少 2GB RAM
- 存储空间: 至少 1GB 可用空间
- Ren'Py 引擎 (已包含)

## 安装步骤
1. 下载游戏文件包
2. 解压到任意目录
3. 双击运行 "游戏名称.exe" (Windows) 或 "游戏名称.app" (macOS)
4. 开始游戏体验

## 游戏特色
- 基于真实文化遗产的严肃游戏
- AI生成的丰富剧情和对话
- 沉浸式的视觉和音频体验
- 教育性与娱乐性并重

## 技术支持
如遇到问题，请查看游戏目录下的 log.txt 文件
"""
        
        guide_path = os.path.join(self.output_dir, "README.txt")
        with open(guide_path, 'w', encoding='utf-8') as f:
            f.write(guide)
        
        return guide

    def _generate_completion_report(self, results: Dict) -> Dict:
        """生成完成报告"""
        total_time = time.time() - self.pipeline_state["start_time"]
        
        report = {
            "generation_summary": {
                "total_time": total_time,
                "steps_completed": self.pipeline_state["current_step"],
                "success": True
            },
            "content_statistics": {
                "sad_files": len(results.get("preprocessing", {}).get("sad_files", [])),
                "vsi_files": len(results.get("preprocessing", {}).get("vsi_files", [])),
                "story_nodes": len(results.get("narratives", [])),
                "characters": len(results.get("framework", {}).get("characters", [])),
                "total_dialogues": sum(len(n.get("narrative_content", {}).get("dialogues", [])) for n in results.get("narratives", [])),
                "generated_images": sum(
                    len(r.get("backgrounds", [])) + len(r.get("characters", [])) + len(r.get("cg_images", []))
                    for r in results.get("images", [])
                ),
                "generated_audio": sum(
                    len(r.get("music_files", [])) + len(r.get("voice_files", [])) + len(r.get("sound_files", []))
                    for r in results.get("audio", [])
                )
            },
            "step_times": self.pipeline_state["step_times"],
            "output_location": self.output_dir
        }
        
        # 保存报告
        report_path = os.path.join(self.output_dir, "generation_report.json")
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        return report

    def _save_error_report(self, error: Exception, partial_results: Dict):
        """保存错误报告"""
        error_report = {
            "error_message": str(error),
            "error_type": type(error).__name__,
            "failed_step": self.pipeline_state["current_step"],
            "partial_results": {k: len(v) if isinstance(v, (list, dict)) else str(v) for k, v in partial_results.items()},
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        error_path = os.path.join(self.output_dir, "error_report.json")
        with open(error_path, 'w', encoding='utf-8') as f:
            json.dump(error_report, f, ensure_ascii=False, indent=2)
