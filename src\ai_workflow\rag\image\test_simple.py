"""
简单测试脚本 - 验证增强图像生成器基本功能
"""

import os
import sys

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

def test_imports():
    """测试模块导入"""
    print("Testing imports...")
    
    try:
        from src.ai_workflow.rag.image.clip_image_indexer import CLIPImageIndexer
        print("✓ CLIPImageIndexer imported successfully")
    except Exception as e:
        print(f"✗ Failed to import CLIPImageIndexer: {e}")
        return False
    
    try:
        from src.ai_workflow.rag.image.text_image_retriever import TextImageRetriever
        print("✓ TextImageRetriever imported successfully")
    except Exception as e:
        print(f"✗ Failed to import TextImageRetriever: {e}")
        return False
    
    try:
        from src.ai_workflow.rag.image.enhanced_image_generator import EnhancedImageGenerator
        print("✓ EnhancedImageGenerator imported successfully")
    except Exception as e:
        print(f"✗ Failed to import EnhancedImageGenerator: {e}")
        return False
    
    return True


def test_model_paths():
    """测试模型路径"""
    print("\nTesting model paths...")
    
    clip_model_path = "src/ai_workflow/models/clip-vit-base-patch16"
    sd_model_path = "src/ai_workflow/models/anything-v5"
    
    if os.path.exists(clip_model_path):
        print(f"✓ CLIP model path exists: {clip_model_path}")
        
        # 检查关键文件
        config_file = os.path.join(clip_model_path, "config.json")
        model_file = os.path.join(clip_model_path, "pytorch_model.bin")
        
        if os.path.exists(config_file):
            print("  ✓ config.json found")
        else:
            print("  ✗ config.json missing")
        
        if os.path.exists(model_file):
            print("  ✓ pytorch_model.bin found")
        else:
            print("  ✗ pytorch_model.bin missing")
    else:
        print(f"✗ CLIP model path not found: {clip_model_path}")
    
    if os.path.exists(sd_model_path):
        print(f"✓ SD model path exists: {sd_model_path}")
        
        model_file = os.path.join(sd_model_path, "anything-v5.safetensors")
        if os.path.exists(model_file):
            print("  ✓ anything-v5.safetensors found")
            file_size = os.path.getsize(model_file) / (1024 * 1024 * 1024)
            print(f"  ✓ Model size: {file_size:.2f} GB")
        else:
            print("  ✗ anything-v5.safetensors missing")
    else:
        print(f"✗ SD model path not found: {sd_model_path}")


def test_directories():
    """测试目录结构"""
    print("\nTesting directories...")
    
    input_dir = "src/ai_workflow/input/image"
    output_dir = "src/ai_workflow/output/image"
    index_dir = "src/ai_workflow/rag/image/index_data"
    
    # 检查输入目录
    if os.path.exists(input_dir):
        print(f"✓ Input directory exists: {input_dir}")
        image_files = [f for f in os.listdir(input_dir) 
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.bmp', '.tiff'))]
        print(f"  Found {len(image_files)} image files")
        if image_files:
            print(f"  Examples: {', '.join(image_files[:3])}")
    else:
        print(f"✗ Input directory not found: {input_dir}")
        os.makedirs(input_dir, exist_ok=True)
        print(f"  Created input directory: {input_dir}")
    
    # 检查输出目录
    if os.path.exists(output_dir):
        print(f"✓ Output directory exists: {output_dir}")
    else:
        print(f"✗ Output directory not found: {output_dir}")
        os.makedirs(output_dir, exist_ok=True)
        print(f"  Created output directory: {output_dir}")
    
    # 检查索引目录
    if os.path.exists(index_dir):
        print(f"✓ Index directory exists: {index_dir}")
        index_files = os.listdir(index_dir)
        if index_files:
            print(f"  Index files: {', '.join(index_files)}")
        else:
            print("  No index files found")
    else:
        print(f"✗ Index directory not found: {index_dir}")


def test_basic_functionality():
    """测试基本功能"""
    print("\nTesting basic functionality...")
    
    try:
        from src.ai_workflow.rag.image.clip_image_indexer import CLIPImageIndexer
        
        # 初始化索引器
        indexer = CLIPImageIndexer()
        print("✓ CLIPImageIndexer initialized")
        
        # 获取索引信息
        info = indexer.get_index_info()
        print(f"✓ Index info: {info}")
        
        return True
        
    except Exception as e:
        print(f"✗ Basic functionality test failed: {e}")
        return False


def main():
    """主测试函数"""
    print("Enhanced Image Generator - Simple Test")
    print("=" * 50)
    
    # 运行测试
    tests = [
        ("Import Test", test_imports),
        ("Model Paths Test", test_model_paths),
        ("Directories Test", test_directories),
        ("Basic Functionality Test", test_basic_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("Test Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! The module is ready to use.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == len(results)


if __name__ == "__main__":
    main()
