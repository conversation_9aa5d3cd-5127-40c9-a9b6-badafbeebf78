# AI-GAL 后端架构设计文档

## 📋 架构概述

AI-GAL 后端采用 **微服务化的单体架构**，将原有的 CLI 命令行程序改造为基于 FastAPI 的 RESTful API 服务。这种设计既保持了代码的简洁性，又提供了良好的扩展性和可维护性。

## 🎯 设计原则

### 1. 服务化改造原则
- **最小侵入**：保持原有业务逻辑不变，只改变调用方式
- **统一接口**：所有服务通过统一的 `/call` 接口调用
- **松耦合**：前端只需知道服务名和参数，无需了解具体实现

### 2. 架构设计原则
- **单一职责**：每个组件只负责特定的功能
- **依赖倒置**：高层模块不依赖低层模块，都依赖抽象
- **开闭原则**：对扩展开放，对修改封闭

## 🏗️ 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                      │
│                   (API 接口层)                              │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │   server.py │  │  router.py  │  │ middleware  │        │
│  │  (FastAPI)  │  │ (路由分发)   │  │  (中间件)    │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Service Layer                            │
│                   (服务层)                                  │
│  ┌─────────────────────────────────────────────────────┐   │
│  │            service_registry.py                      │   │
│  │           (服务注册与调用管理)                        │   │
│  │                                                     │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │   │
│  │  │   服务注册   │  │   服务发现   │  │   调用管理   │ │   │
│  │  │ @register   │  │ get_services│  │ call_service│ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Business Layer                            │
│                  (业务层)                                   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │              game_services.py                       │   │
│  │             (业务服务封装)                            │   │
│  │                                                     │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐ │   │
│  │  │  游戏生成    │  │  素材处理    │  │  系统测试    │ │   │
│  │  │   服务      │  │    服务      │  │    服务      │ │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘ │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Core Layer                               │
│                   (核心层)                                  │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │ controller/ │  │ generators/ │  │ preprocess/ │        │
│  │ (流程控制)   │  │ (内容生成)   │  │ (素材处理)   │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │
│  │    rag/     │  │   utils/    │  │   tests/    │        │
│  │  (检索增强)  │  │  (工具函数)  │  │   (测试)     │        │
│  └─────────────┘  └─────────────┘  └─────────────┘        │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件详解

### 1. API 接口层 (Presentation Layer)

#### server.py - 服务器主程序
```python
# 职责：
- FastAPI 应用初始化
- 中间件配置 (CORS, 异常处理)
- 生命周期管理
- 服务启动和配置

# 关键特性：
- 自动生成 API 文档
- 全局异常处理
- 健康检查端点
```

#### router.py - 路由管理
```python
# 职责：
- HTTP 请求路由分发
- 请求/响应模型验证
- 异步任务管理
- 任务状态跟踪

# 核心端点：
- POST /api/v1/call          # 同步服务调用
- POST /api/v1/call-async    # 异步服务调用
- GET  /api/v1/task/{id}     # 任务状态查询
- GET  /api/v1/services      # 服务发现
```

### 2. 服务层 (Service Layer)

#### service_registry.py - 服务注册中心
```python
# 设计模式：注册器模式 + 装饰器模式

# 核心功能：
@register_service("service_name", "description", parameters)
def service_function():
    pass

# 服务管理：
- 自动服务注册
- 参数类型验证  
- 服务发现机制
- 统一错误处理
```

**服务注册流程**：
```
1. 使用 @register_service 装饰器标记服务函数
2. 装饰器自动提取函数签名和参数信息
3. 将服务信息存储到全局注册表
4. 提供统一的 call_service() 调用接口
```

### 3. 业务层 (Business Layer)

#### game_services.py - 业务服务封装
```python
# 职责：
- 将 CLI 逻辑转换为纯函数
- 提供统一的返回格式
- 处理业务异常
- 管理业务状态

# 服务分类：
1. 核心业务服务：游戏生成、素材处理
2. 系统管理服务：配置检查、健康监测  
3. 测试服务：功能测试、性能测试
```

**CLI 到服务的转换示例**：
```python
# 原 CLI 代码：
def cli_generate_game():
    theme = input("请输入游戏主题: ")
    materials = input("请输入素材路径: ").split(',')
    print("开始生成游戏...")
    result = controller.generate_game(theme, materials)
    print(f"生成完成: {result}")

# 转换后的服务：
def generate_complete_game(theme: str, material_paths: List[str]) -> Dict:
    try:
        controller = MainPipelineController()
        result = controller.generate_complete_game(material_paths, theme)
        return {
            "status": "success",
            "message": "游戏生成完成", 
            "data": result
        }
    except Exception as e:
        return {
            "status": "error",
            "message": f"游戏生成失败: {str(e)}",
            "data": None
        }
```

### 4. 核心层 (Core Layer)

保持原有的业务逻辑模块不变：
- `controller/` - 主流程控制
- `generators/` - 各种内容生成器
- `preprocess/` - 素材预处理
- `rag/` - 检索增强生成
- `utils/` - 工具函数

## 🔄 数据流向

### 同步调用流程
```
1. 前端发送 HTTP POST 请求到 /api/v1/call
2. router.py 接收请求，验证参数格式
3. 调用 service_registry.call_service()
4. 查找对应的服务函数并执行
5. 服务函数调用核心业务逻辑
6. 返回统一格式的响应给前端
```

### 异步调用流程
```
1. 前端发送 HTTP POST 请求到 /api/v1/call-async
2. router.py 创建任务ID，返回给前端
3. 将任务添加到后台执行队列
4. 前端通过 task_id 轮询任务状态
5. 后台任务完成后更新任务状态
6. 前端获取最终执行结果
```

## 🎯 设计优势

### 1. 统一接口设计
- **简化前端开发**：只需要一个 `/call` 接口
- **避免 RESTful 复杂性**：不需要设计复杂的资源路径
- **参数传递灵活**：通过 JSON 传递任意参数

### 2. 服务注册机制
- **自动服务发现**：新服务只需添加装饰器
- **参数验证**：自动提取和验证函数参数
- **文档生成**：自动生成服务文档

### 3. 异步任务支持
- **长任务处理**：支持游戏生成等长时间任务
- **状态跟踪**：实时查询任务执行状态
- **资源管理**：避免阻塞服务器资源

### 4. 松耦合架构
- **业务逻辑隔离**：核心逻辑与接口层分离
- **易于测试**：每层都可以独立测试
- **便于维护**：修改一层不影响其他层

## 🚀 扩展性设计

### 添加新服务
```python
# 1. 在 game_services.py 中添加业务函数
def new_business_function(param1: str, param2: int) -> Dict:
    # 业务逻辑
    return {"status": "success", "data": result}

# 2. 在 service_registry.py 中注册服务
@register_service("new_service", "新服务描述")
def new_service_wrapper(param1: str, param2: int):
    return _game_services.new_business_function(param1, param2)
```

### 添加新的生成器
```python
# 1. 在 src/generators/ 中添加新生成器
class NewGenerator:
    def generate(self, params):
        # 生成逻辑
        pass

# 2. 在 game_services.py 中添加对应服务
def use_new_generator(params):
    generator = NewGenerator()
    return generator.generate(params)
```

## 📊 性能考虑

### 1. 异步处理
- 长时间任务使用异步调用
- 避免阻塞主线程
- 支持任务取消和超时

### 2. 资源管理
- 合理的超时设置
- 任务状态清理机制
- 内存使用监控

### 3. 并发控制
- FastAPI 内置异步支持
- 可配置工作进程数
- 请求限流机制

这种架构设计既保持了原有代码的稳定性，又提供了现代化的 API 接口，为前端开发提供了便利的调用方式。
