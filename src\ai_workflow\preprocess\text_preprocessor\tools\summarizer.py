"""
摘要生成器类
封装调用大语言模型生成摘要的功能
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
import time

try:
    import openai
    from openai import OpenAI
except ImportError:
    openai = None
    OpenAI = None

try:
    import requests
except ImportError:
    requests = None


class Summarizer:
    """
    摘要生成器类，封装调用大语言模型（如 OpenAI GPT）的摘要功能
    """
    
    def __init__(self, output_dir: str = "summaries",
                 api_type: str = "openai",
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None,
                 model: str = "deepseek-reasoner"):
        """
        初始化摘要生成器

        Args:
            output_dir: 摘要文件保存目录
            api_type: API类型，支持 "openai", "azure", "custom"
            api_key: API密钥
            base_url: 自定义API基础URL
            model: 默认使用的模型名称
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)

        self.api_type = api_type
        self.api_key = api_key
        self.base_url = base_url
        self.default_model = model
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化API客户端
        self.client = None
        self._init_client()
        
        # 默认提示模板
        self.default_prompt = """请为以下文本生成一个简洁的摘要，要求：
1. 概括主要内容和核心观点
2. 保持逻辑清晰，语言简洁
3. 长度控制在200-300字
4. 保留重要的关键信息

文本内容：
{text}

摘要："""
    
    def _init_client(self):
        """初始化API客户端"""
        if self.api_type == "openai":
            if openai is None or OpenAI is None:
                self.logger.warning("openai库未安装，无法使用OpenAI API")
                return

            # 使用新的OpenAI客户端初始化方式
            try:
                self.client = OpenAI(
                    api_key=self.api_key,
                    base_url=self.base_url
                )
                self.logger.info("OpenAI客户端初始化成功")
            except Exception as e:
                self.logger.error(f"OpenAI客户端初始化失败: {str(e)}")
                self.client = None

        self.logger.info(f"摘要生成器初始化完成，API类型: {self.api_type}")
    
    def summarize(self, text: str, custom_prompt: Optional[str] = None,
                 max_length: int = 300, model: Optional[str] = None) -> Dict[str, Any]:
        """
        生成文本摘要
        
        Args:
            text: 要摘要的文本
            custom_prompt: 自定义提示词
            max_length: 最大摘要长度
            model: 使用的模型名称
            
        Returns:
            包含摘要和元数据的字典
        """
        if not text.strip():
            raise ValueError("输入文本不能为空")

        # 使用传入的模型或默认模型
        model_to_use = model if model is not None else self.default_model

        # 选择提示词
        prompt = custom_prompt if custom_prompt else self.default_prompt
        full_prompt = prompt.format(text=text)
        
        try:
            if self.api_type == "openai":
                summary = self._summarize_openai(full_prompt, model_to_use, max_length)
            else:
                summary = self._summarize_custom(full_prompt, model_to_use, max_length)

            result = {
                'summary': summary,
                'original_length': len(text),
                'summary_length': len(summary),
                'compression_ratio': len(summary) / len(text),
                'model': model_to_use,
                'timestamp': time.time()
            }
            
            self.logger.info(f"摘要生成完成，原文{len(text)}字符，摘要{len(summary)}字符")
            return result
            
        except Exception as e:
            self.logger.error(f"摘要生成失败: {str(e)}")
            raise
    
    def _summarize_openai(self, prompt: str, model: str, max_length: int) -> str:
        """
        使用OpenAI API生成摘要

        Args:
            prompt: 完整提示词
            model: 模型名称
            max_length: 最大长度

        Returns:
            生成的摘要
        """
        if self.client is None:
            raise ImportError("OpenAI客户端未初始化，请检查API密钥和网络连接")

        try:
            # 使用新的OpenAI 1.x API调用方式
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=max_length,
                temperature=0.3,
                top_p=0.9
            )

            summary = response.choices[0].message.content.strip()
            return summary

        except Exception as e:
            self.logger.error(f"OpenAI API调用失败: {str(e)}")
            raise
    
    def _summarize_custom(self, prompt: str, model: str, max_length: int) -> str:
        """
        使用自定义API生成摘要
        
        Args:
            prompt: 完整提示词
            model: 模型名称
            max_length: 最大长度
            
        Returns:
            生成的摘要
        """
        if requests is None:
            raise ImportError("需要安装requests库: pip install requests")
        
        if not self.base_url:
            raise ValueError("使用自定义API需要设置base_url")
        
        headers = {
            "Content-Type": "application/json"
        }
        
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        
        data = {
            "model": model,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": max_length,
            "temperature": 0.3
        }
        
        try:
            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            summary = result["choices"][0]["message"]["content"].strip()
            return summary
            
        except Exception as e:
            self.logger.error(f"自定义API调用失败: {str(e)}")
            raise
    
    def summarize_segments(self, segments: List[Dict[str, Any]], 
                          source_name: str) -> List[Dict[str, Any]]:
        """
        批量生成章节摘要
        
        Args:
            segments: 章节列表
            source_name: 源文件名
            
        Returns:
            包含摘要的章节列表
        """
        summarized_segments = []
        
        for i, segment in enumerate(segments):
            try:
                # 生成摘要
                summary_result = self.summarize(segment['text'])
                
                # 更新段落信息
                segment_with_summary = segment.copy()
                segment_with_summary.update(summary_result)
                
                # 保存摘要到文件
                summary_file = self.output_dir / f"{source_name}_seg{i+1}_summary.txt"
                with open(summary_file, 'w', encoding='utf-8') as f:
                    f.write(summary_result['summary'])
                
                segment_with_summary['summary_file'] = str(summary_file)
                summarized_segments.append(segment_with_summary)
                
                self.logger.info(f"章节{i+1}摘要生成完成")
                
                # 添加延迟避免API限制
                time.sleep(1)
                
            except Exception as e:
                self.logger.error(f"章节{i+1}摘要生成失败: {str(e)}")
                # 即使失败也保留原始段落
                summarized_segments.append(segment)
        
        # 保存批量摘要元数据
        metadata_file = self.output_dir / f"{source_name}_summaries_metadata.json"
        with open(metadata_file, 'w', encoding='utf-8') as f:
            json.dump(summarized_segments, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"批量摘要生成完成: {source_name}, 共{len(summarized_segments)}个章节")
        return summarized_segments
    
    def create_overall_summary(self, segments_summaries: List[str], 
                              source_name: str) -> Dict[str, Any]:
        """
        基于章节摘要生成整体摘要
        
        Args:
            segments_summaries: 章节摘要列表
            source_name: 源文件名
            
        Returns:
            整体摘要结果
        """
        combined_text = "\n\n".join(segments_summaries)
        
        overall_prompt = """以下是一个文档各章节的摘要，请基于这些摘要生成一个整体的文档摘要：

章节摘要：
{text}

请生成一个整体摘要，要求：
1. 概括整个文档的主要内容和结构
2. 突出核心主题和重要观点
3. 保持逻辑连贯，语言简洁
4. 长度控制在400-500字

整体摘要："""
        
        try:
            result = self.summarize(combined_text, overall_prompt, max_length=600)
            
            # 保存整体摘要
            overall_summary_file = self.output_dir / f"{source_name}_overall_summary.txt"
            with open(overall_summary_file, 'w', encoding='utf-8') as f:
                f.write(result['summary'])
            
            result['summary_file'] = str(overall_summary_file)
            result['source_name'] = source_name
            
            self.logger.info(f"整体摘要生成完成: {source_name}")
            return result
            
        except Exception as e:
            self.logger.error(f"整体摘要生成失败: {str(e)}")
            raise


if __name__ == "__main__":
    # 测试代码
    summarizer = Summarizer()
    
    # 示例用法
    print("Summarizer类已创建")
    print("使用方法:")
    print("1. 单个摘要: summarizer.summarize(text)")
    print("2. 批量摘要: summarizer.summarize_segments(segments, source_name)")
    print("3. 整体摘要: summarizer.create_overall_summary(summaries, source_name)")
