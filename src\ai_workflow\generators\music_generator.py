"""
简化的音乐生成器
仅支持本地MusicGen模型推理
"""

import os
from typing import Dict, List
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 可选导入（用于本地音乐生成）
try:
    import torch
    import numpy as np
    from transformers import MusicgenForConditionalGeneration, AutoProcessor
    import scipy.io.wavfile as wavfile
    MUSICGEN_AVAILABLE = True
except ImportError:
    MUSICGEN_AVAILABLE = False

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

class MusicGenerator:
    """简化的音乐生成器类 - 仅支持本地MusicGen推理"""

    def __init__(self):
        """初始化音乐生成器"""
        # 音乐输出目录
        self.music_directory = os.path.join(game_directory, "music")
        os.makedirs(self.music_directory, exist_ok=True)

        # 默认模型
        self.model_name = "facebook/musicgen-medium"

        print(f"音乐生成器初始化完成")
        print(f"输出目录: {self.music_directory}")
        print(f"MusicGen可用: {MUSICGEN_AVAILABLE}")

    def generate_music(self, prompt: str, filename: str, style: str = "common") -> str:
        """
        生成单首音乐

        Args:
            prompt: 音乐生成提示词
            filename: 输出音乐文件名（不含扩展名）
            style: 音乐风格

        Returns:
            生成结果状态："ok"表示成功，"error"表示失败
        """
        if not MUSICGEN_AVAILABLE:
            print("MusicGen依赖不可用")
            return "error"

        try:
            print(f"使用本地MusicGen生成音乐: {filename}")
            return self._generate_local_music(prompt, filename, style)
        except Exception as e:
            print(f"音乐生成失败: {e}")
            return "error"

    def _generate_local_music(self, prompt: str, filename: str, style: str) -> str:
        """
        本地音乐生成 (使用 Meta MusicGen)

        Args:
            prompt: 音乐生成提示词
            filename: 输出文件名（不含扩展名）
            style: 音乐风格

        Returns:
            "ok" 或 "error"
        """
        try:
            print(f"使用MusicGen模型: {self.model_name}")

            # 检查设备
            device = "cuda" if torch.cuda.is_available() else "cpu"
            print(f"使用设备: {device}")

            # 构建提示词
            style_prompts = {
                "sad": "sad, melancholy, piano, emotional",
                "common": "peaceful, background, ambient",
                "epic": "epic, orchestral, dramatic",
                "ambient": "ambient, atmospheric, calm",
                "upbeat": "upbeat, energetic, cheerful"
            }

            style_text = style_prompts.get(style, style_prompts["common"])
            final_prompt = f"{prompt}, {style_text}" if prompt else style_text

            print(f"生成提示词: {final_prompt}")

            # 计算本地模型路径
            current_file_dir = os.path.dirname(__file__)  # generators目录
            ai_workflow_dir = os.path.dirname(current_file_dir)  # ai_workflow目录
            local_model_path = os.path.join(ai_workflow_dir, "models", "musicgen-medium")
            
            print(f"尝试加载本地模型: {local_model_path}")
            
            # 检查本地模型是否存在
            if os.path.exists(local_model_path):
                print("使用本地模型")
                model_path = local_model_path
            else:
                print("本地模型不存在")
                return "error"
            
            # 加载模型和处理器
            processor = AutoProcessor.from_pretrained(model_path)
            model = MusicgenForConditionalGeneration.from_pretrained(
                model_path,
                torch_dtype=torch.float16,
                device_map="auto"
            )

            # 准备输入
            inputs = processor(
                text=[final_prompt],
                padding=True,
                return_tensors="pt"
            )

            # 移动到设备
            if device == "cuda":
                inputs = {k: v.to(device) for k, v in inputs.items()}
                model = model.to(device)

            # 生成音乐
            print("开始生成音乐...")
            with torch.no_grad():
                audio_values = model.generate(
                    **inputs,
                    max_new_tokens=1024,  # 约30秒的音乐
                    do_sample=True,
                    guidance_scale=3.0,
                    temperature=1.0
                )

            # 转换为numpy数组
            audio_data = audio_values[0, 0].cpu().numpy()

            # 获取采样率
            sampling_rate = model.config.audio_encoder.sampling_rate

            # 确保输出文件有正确的扩展名
            if not filename.endswith('.wav'):
                filename += '.wav'

            output_path = os.path.join(self.music_directory, filename)

            # 保存音频文件
            audio_data = np.clip(audio_data, -1.0, 1.0)
            audio_int16 = (audio_data * 32767).astype(np.int16)
            wavfile.write(output_path, sampling_rate, audio_int16)

            print(f"本地音乐生成成功: {output_path}")
            print(f"音频长度: {len(audio_data) / sampling_rate:.2f} 秒")

            # 清理内存
            del model
            del processor
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            return "ok"

        except Exception as e:
            print(f"本地音乐生成失败: {e}")
            return "error"

    def generate_music_for_node(self, narrative_data: Dict) -> Dict:
        """
        为单个节点生成所需的音乐 (与main_pipeline_controller对接)

        Args:
            narrative_data: 节点叙事数据，包含音乐指令等

        Returns:
            包含生成音乐信息的字典
        """
        node_id = narrative_data.get("node_id", "unknown")
        narrative_content = narrative_data.get("narrative_content", {})

        result = {
            "node_id": node_id,
            "music_files": [],
            "generation_status": "success"
        }

        try:
            # 获取音乐指令
            music_instructions = narrative_content.get("music_instructions", [])

            if music_instructions:
                for i, instruction in enumerate(music_instructions):
                    music_name = f"bgm_{node_id}_{i+1}"

                    # 简单的风格判断
                    style = "sad" if any(word in instruction.lower() for word in ["sad", "悲伤", "忧郁"]) else "common"

                    # 生成音乐
                    status = self.generate_music(instruction, music_name, style)

                    result["music_files"].append({
                        "name": music_name,
                        "instruction": instruction,
                        "style": style,
                        "file_path": f"{self.music_directory}/{music_name}.wav",
                        "status": status
                    })
            else:
                # 生成默认背景音乐
                default_music_name = f"bgm_{node_id}_default"
                status = self.generate_music("peaceful background music", default_music_name, "common")

                result["music_files"].append({
                    "name": default_music_name,
                    "instruction": "默认背景音乐",
                    "style": "common",
                    "file_path": f"{self.music_directory}/{default_music_name}.wav",
                    "status": status
                })

        except Exception as e:
            print(f"节点 {node_id} 音乐生成失败: {e}")
            result["generation_status"] = "error"
            result["error_message"] = str(e)

        return result


# 为了保持向后兼容性，提供便捷函数
def generate_music(prompt: str, filename: str, style: str = "common") -> str:
    """
    统一的音乐生成函数（向后兼容）

    Args:
        prompt: 音乐生成提示词
        filename: 输出音乐文件名
        style: 音乐风格

    Returns:
        生成结果状态
    """
    generator = MusicGenerator()
    return generator.generate_music(prompt, filename, style)


if __name__ == "__main__":
    # 测试代码
    generator = MusicGenerator()
    print(f"MusicGen可用: {MUSICGEN_AVAILABLE}")

    if MUSICGEN_AVAILABLE:
        # 测试生成
        test_result = generator.generate_music(
            "peaceful background music for cultural heritage game",
            "test_music",
            "common"
        )
        print(f"测试生成结果: {test_result}")
    else:
        print("请先安装MusicGen依赖: pip install torch transformers scipy")