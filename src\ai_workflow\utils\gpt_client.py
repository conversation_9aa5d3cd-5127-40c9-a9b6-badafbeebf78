import requests
import configparser
import re
import json
import os

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

# 更新配置文件路径
config = configparser.ConfigParser()
config.read(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "config", "config.ini"), encoding='utf-8')

import json
import requests
import re

#todo 链接记得填，把链接的位置改到GUI里面
def gpt(system, prompt, mode="common", history=None):
    gpt_key = config.get('CHATGPT', 'GPT_KEY')
    gpt_url = config.get('CHATGPT', 'BASE_URL')
    model = config.get('CHATGPT', 'model')

    messages = [
        {"role": "system", "content": system},
        {"role": "user", "content": prompt}
    ]

    if history:
        messages = history + messages

    headers = {
        "Authorization": f"Bearer {gpt_key}",
        "Content-Type": "application/json"
    }

    data = {
        "model": model,
        "messages": messages,
        "temperature": 0.7
    }

    try:
        response = requests.post(gpt_url, headers=headers, json=data)
        response.raise_for_status()
        result = response.json()
        return result['choices'][0]['message']['content']
    except requests.exceptions.RequestException as e:
        print(f"GPT API请求失败: {e}")
        return "API调用失败，请检查网络连接和配置"
    except KeyError as e:
        print(f"GPT API响应格式错误: {e}")
        return "API响应格式错误"
    except Exception as e:
        print(f"GPT调用出现未知错误: {e}")
        return "未知错误"
