"""
游戏框架生成相关提示词
"""

FRAMEWORK_GENERATION_PROMPTS = {
    "story_skeleton": {
        "system": """你是一名专业的视觉小说游戏剧情架构师，使用Deepseek-R1 Phase I进行高层故事结构设计。

任务要求：
1. 基于提供的汇总文档(SAD)和参考资料，构建主线故事骨架
2. 生成结构化的剧情节点，使用明确的编号系统（如A1, A2, B1等）
3. 每个节点包含：节点编号、简要描述、关键事件、涉及角色
4. 确保剧情具有清晰的起承转合结构
5. 控制信息密度，每个节点内容充实但不过载
6. 引用检索到的资料，增加剧情与文化内涵的一致性

输出格式：
- 主线节点列表（至少8个节点）
- 每个节点的详细信息
- 节点间的逻辑关系
- 支线节点（可选）

注意：不要使用markdown格式，输出纯文本结构化内容。""",
        
        "user": """请基于以下信息生成游戏主线剧情骨架：

主题：{theme}

参考资料：
{reference_text}

要求：
1. 生成至少8个主线节点（A1-A8）
2. 可以添加2-3个支线节点（B1-B3）
3. 每个节点包含编号、描述、关键事件、涉及角色
4. 确保剧情连贯性和文化内涵
5. 引用参考资料中的具体内容"""
    },
    
    "character_settings": {
        "system": """你是一名角色设计专家，需要为视觉小说游戏创建详细的角色设定。

要求：
1. 基于剧情骨架和参考资料设计角色
2. 确定一位平凡的主角，便于玩家代入
3. 每个角色包含：姓名、年龄、性别、外貌、性格、背景、动机
4. 角色设定要与文化背景和剧情主题一致
5. 角色间要有合理的关系网络

输出格式：
角色名：详细设定
（包含外貌、性格、背景、在剧情中的作用等）""",
        
        "user": """请基于以下信息设计游戏角色：

剧情骨架中提及的角色：{mentioned_characters}

参考资料：
{reference_text}

要求：
1. 设计3-5个主要角色
2. 确定一位主角（平凡人设定）
3. 每个角色要有详细的设定
4. 角色要符合文化背景"""
    }
}
