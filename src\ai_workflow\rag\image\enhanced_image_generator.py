"""
基于CLIP与IP-Adapter的图像检索增强生成模块
整合CLIP图像检索和IP-Adapter图像生成功能
"""

import os
import time
from datetime import datetime
from PIL import Image
from typing import Optional, Tuple, List, Dict, Any
import logging

from clip_image_indexer import CLIPImageIndexer
from text_image_retriever import TextImageRetriever
from ip_adapter_generator import IPAdapterGenerator

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedImageGenerator:
    """基于CLIP与IP-Adapter的图像检索增强生成器"""
    
    def __init__(
        self,
        clip_model_path: str = None,
        sd_model_path: str = None,
        similarity_threshold: float = 0.2,
        ip_adapter_scale: float = 0.5,
        auto_build_index: bool = True
    ):
        """
        初始化增强图像生成器
        
        Args:
            clip_model_path: CLIP模型路径
            sd_model_path: Stable Diffusion模型路径
            similarity_threshold: 图像检索相似度阈值
            ip_adapter_scale: IP-Adapter影响权重
            auto_build_index: 是否自动构建索引
        """
        self.similarity_threshold = similarity_threshold
        self.ip_adapter_scale = ip_adapter_scale
        
        # 设置输出目录
        self.output_dir = "src/ai_workflow/output/image"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 设置输入目录
        self.input_dir = "src/ai_workflow/input/image"
        
        # 初始化组件
        logger.info("Initializing Enhanced Image Generator...")
        
        # 初始化文本图像检索器
        self.retriever = TextImageRetriever(
            model_path=clip_model_path,
            similarity_threshold=similarity_threshold
        )
        
        # 初始化IP-Adapter生成器
        self.generator = IPAdapterGenerator(model_path=sd_model_path)
        self.generator.set_ip_adapter_scale(ip_adapter_scale)
        
        # 自动构建索引（如果需要）
        if auto_build_index:
            self._ensure_index_ready()
        
        logger.info("Enhanced Image Generator initialized successfully")
    
    def _ensure_index_ready(self):
        """确保索引已准备就绪"""
        stats = self.retriever.get_retrieval_stats()
        
        if stats.get("total_images", 0) == 0:
            logger.info("No existing index found. Building index...")
            if os.path.exists(self.input_dir):
                if self.retriever.rebuild_index(self.input_dir):
                    logger.info("Index built successfully")
                else:
                    logger.warning("Failed to build index")
            else:
                logger.warning(f"Input directory not found: {self.input_dir}")
        else:
            logger.info(f"Index loaded with {stats['total_images']} images")
    
    def generate_enhanced_image(
        self,
        text_prompt: str,
        negative_prompt: str = None,
        width: int = 512,
        height: int = 768,
        num_inference_steps: int = 25,
        guidance_scale: float = 7.5,
        seed: Optional[int] = None,
        use_image_enhancement: bool = True,
        save_image: bool = True,
        filename_prefix: str = None
    ) -> Tuple[Image.Image, Dict[str, Any]]:
        """
        生成增强图像
        
        Args:
            text_prompt: 文本提示
            negative_prompt: 负面提示
            width: 图像宽度
            height: 图像高度
            num_inference_steps: 推理步数
            guidance_scale: 引导强度
            seed: 随机种子
            use_image_enhancement: 是否使用图像增强
            save_image: 是否保存图像
            filename_prefix: 文件名前缀
            
        Returns:
            (生成的图像, 生成信息)
        """
        generation_info = {
            "text_prompt": text_prompt,
            "use_image_enhancement": use_image_enhancement,
            "retrieved_image": None,
            "similarity_score": None,
            "generation_time": None,
            "output_path": None
        }
        
        start_time = time.time()
        
        try:
            # 检索相似图像（如果启用图像增强）
            retrieved_image_path = None
            similarity_score = None
            
            if use_image_enhancement:
                logger.info(f"Searching for similar images for: '{text_prompt}'")
                search_result = self.retriever.search_best_match(text_prompt)
                
                if search_result:
                    retrieved_image_path, similarity_score = search_result
                    generation_info["retrieved_image"] = retrieved_image_path
                    generation_info["similarity_score"] = similarity_score
                    logger.info(f"Found similar image: {os.path.basename(retrieved_image_path)} (similarity: {similarity_score:.3f})")
                else:
                    logger.info("No similar images found above threshold, using text-only generation")
            
            # 生成图像
            if retrieved_image_path and use_image_enhancement:
                # 使用图像增强生成
                logger.info("Generating image with image enhancement")
                generated_image = self.generator.generate_with_image_prompt(
                    text_prompt=text_prompt,
                    image_prompt=retrieved_image_path,
                    negative_prompt=negative_prompt,
                    width=width,
                    height=height,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    seed=seed
                )
            else:
                # 仅使用文本生成
                logger.info("Generating image with text only")
                generated_image = self.generator.generate_text_only(
                    text_prompt=text_prompt,
                    negative_prompt=negative_prompt,
                    width=width,
                    height=height,
                    num_inference_steps=num_inference_steps,
                    guidance_scale=guidance_scale,
                    seed=seed
                )
            
            generation_time = time.time() - start_time
            generation_info["generation_time"] = generation_time
            
            # 保存图像
            if save_image:
                output_path = self._save_generated_image(
                    generated_image,
                    text_prompt,
                    retrieved_image_path,
                    filename_prefix
                )
                generation_info["output_path"] = output_path
            
            logger.info(f"Image generation completed in {generation_time:.2f} seconds")
            return generated_image, generation_info
            
        except Exception as e:
            logger.error(f"Failed to generate enhanced image: {e}")
            raise
    
    def _save_generated_image(
        self,
        image: Image.Image,
        text_prompt: str,
        retrieved_image_path: Optional[str] = None,
        filename_prefix: str = None
    ) -> str:
        """
        保存生成的图像
        
        Args:
            image: 生成的图像
            text_prompt: 文本提示
            retrieved_image_path: 检索到的图像路径
            filename_prefix: 文件名前缀
            
        Returns:
            保存的文件路径
        """
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 提取关键词作为文件名的一部分
        keywords = text_prompt.replace(" ", "_").replace(",", "")[:20]
        keywords = "".join(c for c in keywords if c.isalnum() or c in "_-")
        
        # 构建文件名
        filename_parts = []
        
        if filename_prefix:
            filename_parts.append(filename_prefix)
        
        filename_parts.append(keywords)
        
        if retrieved_image_path:
            source_name = os.path.splitext(os.path.basename(retrieved_image_path))[0]
            filename_parts.append(f"src_{source_name}")
        
        filename_parts.append(timestamp)
        filename_parts.append("gen.png")
        
        filename = "_".join(filename_parts)
        output_path = os.path.join(self.output_dir, filename)
        
        # 保存图像
        image.save(output_path, "PNG")
        logger.info(f"Generated image saved to: {output_path}")
        
        return output_path
    
    def batch_generate(
        self,
        text_prompts: List[str],
        **generation_kwargs
    ) -> List[Tuple[Image.Image, Dict[str, Any]]]:
        """
        批量生成图像
        
        Args:
            text_prompts: 文本提示列表
            **generation_kwargs: 生成参数
            
        Returns:
            生成结果列表
        """
        results = []
        
        for i, prompt in enumerate(text_prompts):
            logger.info(f"Generating image {i+1}/{len(text_prompts)}: '{prompt[:50]}...'")
            
            try:
                result = self.generate_enhanced_image(
                    text_prompt=prompt,
                    filename_prefix=f"batch_{i+1:03d}",
                    **generation_kwargs
                )
                results.append(result)
            except Exception as e:
                logger.error(f"Failed to generate image for prompt '{prompt}': {e}")
                results.append((None, {"error": str(e)}))
        
        return results
    
    def rebuild_index(self) -> bool:
        """
        重新构建图像索引
        
        Returns:
            是否成功重建
        """
        logger.info("Rebuilding image index...")
        return self.retriever.rebuild_index(self.input_dir)
    
    def update_settings(
        self,
        similarity_threshold: Optional[float] = None,
        ip_adapter_scale: Optional[float] = None
    ):
        """
        更新设置
        
        Args:
            similarity_threshold: 新的相似度阈值
            ip_adapter_scale: 新的IP-Adapter权重
        """
        if similarity_threshold is not None:
            self.similarity_threshold = similarity_threshold
            self.retriever.update_similarity_threshold(similarity_threshold)
        
        if ip_adapter_scale is not None:
            self.ip_adapter_scale = ip_adapter_scale
            self.generator.set_ip_adapter_scale(ip_adapter_scale)
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            系统信息字典
        """
        retrieval_stats = self.retriever.get_retrieval_stats()
        generator_info = self.generator.get_generator_info()
        
        return {
            "retrieval": retrieval_stats,
            "generation": generator_info,
            "settings": {
                "similarity_threshold": self.similarity_threshold,
                "ip_adapter_scale": self.ip_adapter_scale,
                "output_dir": self.output_dir,
                "input_dir": self.input_dir
            }
        }


def main():
    """测试函数"""
    try:
        # 初始化增强生成器
        generator = EnhancedImageGenerator(
            similarity_threshold=0.1,
            ip_adapter_scale=0.5
        )
        
        # 显示系统信息
        info = generator.get_system_info()
        print("System Info:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 测试生成
        test_prompts = [
            "传统中国花纹，精美刺绣，高质量",
            "古代建筑，宫殿，中国风"
        ]
        
        for prompt in test_prompts:
            print(f"\nGenerating image for: '{prompt}'")
            
            image, gen_info = generator.generate_enhanced_image(
                text_prompt=prompt,
                width=512,
                height=512,
                num_inference_steps=20
            )
            
            print(f"Generation info: {gen_info}")
        
    except Exception as e:
        print(f"Test failed: {e}")


if __name__ == "__main__":
    main()
