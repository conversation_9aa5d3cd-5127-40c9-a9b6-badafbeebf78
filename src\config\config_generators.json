{"image_generation": {"use_cloud": false, "cloud_api_key": "", "cloud_base_url": "", "local_url": "http://127.0.0.1:7860", "output_directory": "images", "default_style": "realistic", "image_size": "512x512", "batch_size": 1, "timeout": 300, "enhanced_generation": {"enabled": true, "clip_model_path": "src/ai_workflow/models/clip-vit-base-patch16", "sd_model_path": "src/ai_workflow/models/anything-v5", "similarity_threshold": 0.2, "ip_adapter_scale": 0.5, "auto_build_index": true, "use_image_enhancement": true, "generation_settings": {"width": 512, "height": 768, "num_inference_steps": 25, "guidance_scale": 7.5}}}, "vocal_generation": {"use_cloud": false, "cloud_api_key": "", "cloud_base_url": "", "local_url": "http://127.0.0.1:9880", "sovits_version": "V1", "output_directory": "audio", "default_speaker": 1, "audio_format": "wav", "sample_rate": 22050, "timeout": 180}, "music_generation": {"enabled": true, "use_cloud": false, "cloud_api_key": "", "cloud_base_url": "", "local_url": "http://127.0.0.1:7861", "local_model_path": "", "output_directory": "music", "default_style": "common", "audio_format": "mp3", "timeout": 600, "styles": {"sad": "Pure music, light music, game, galgame, piano, sad, violin, melancholy", "common": "Pure music, light music, relaxed, cafe, game, galgame, piano, peaceful", "epic": "Epic, orchestral, cinematic, dramatic, game, adventure, heroic", "ambient": "Ambient, atmospheric, peaceful, meditation, background, soft", "upbeat": "Upbeat, energetic, positive, cheerful, game, electronic, happy"}}, "narrative_generation": {"model": "deepseek-reasoner", "temperature": 0.7, "max_tokens": 4000, "timeout": 120, "output_directory": "narratives", "enable_rag": true, "rag_top_k": 3, "enable_character_consistency": true}, "framework_generation": {"model": "deepseek-reasoner", "temperature": 0.8, "max_tokens": 6000, "timeout": 180, "output_directory": "frameworks", "enable_rag": true, "rag_top_k": 5, "default_node_count": 8, "enable_branching": true}, "renpy_integration": {"output_directory": "renpy_game", "script_filename": "script.rpy", "enable_gui_generation": true, "enable_options_generation": true, "character_prefix": "define", "label_prefix": "label"}, "ui_design": {"output_directory": "ui", "theme": "cultural_heritage", "enable_responsive": true, "color_scheme": "traditional", "font_family": "default"}}