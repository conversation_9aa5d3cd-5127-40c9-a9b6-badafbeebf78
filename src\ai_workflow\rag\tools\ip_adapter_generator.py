"""
IP-Adapter增强图像生成模块
使用Stable Diffusion和IP-Adapter结合检索到的图像和文本提示生成新图像
"""

import os
import torch
from PIL import Image
from diffusers import StableDiffusionPipeline, DPMSolverMultistepScheduler
from typing import Optional, Union, List
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class IPAdapterGenerator:
    """IP-Adapter增强图像生成器"""
    
    def __init__(self, model_path: str = None, device: str = None):
        """
        初始化IP-Adapter生成器
        
        Args:
            model_path: Stable Diffusion模型路径
            device: 计算设备
        """
        # 设置设备
        if device is None:
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        else:
            self.device = torch.device(device)
        
        # 设置模型路径
        if model_path is None:
            # 使用本地模型路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_path = os.path.join(current_dir, "../../models/anything-v5")
        
        self.model_path = model_path
        self.pipeline = None
        self.ip_adapter_scale = 0.5  # IP-Adapter影响权重
        
        # 初始化管道
        self._load_pipeline()
    
    def _load_pipeline(self):
        """加载Stable Diffusion管道"""
        try:
            logger.info(f"Loading Stable Diffusion pipeline from {self.model_path}")
            
            # 检查模型文件
            model_file = os.path.join(self.model_path, "anything-v5.safetensors")
            if not os.path.exists(model_file):
                logger.error(f"Model file not found: {model_file}")
                raise FileNotFoundError(f"Model file not found: {model_file}")
            
            # 加载管道
            self.pipeline = StableDiffusionPipeline.from_single_file(
                model_file,
                torch_dtype=torch.float16 if self.device.type == "cuda" else torch.float32,
                safety_checker=None,
                requires_safety_checker=False
            )
            
            # 设置调度器
            self.pipeline.scheduler = DPMSolverMultistepScheduler.from_config(
                self.pipeline.scheduler.config
            )
            
            # 移动到设备
            self.pipeline = self.pipeline.to(self.device)
            
            # 启用内存优化
            if self.device.type == "cuda":
                self.pipeline.enable_attention_slicing()
                self.pipeline.enable_model_cpu_offload()
            
            logger.info("Stable Diffusion pipeline loaded successfully")
            
        except Exception as e:
            logger.error(f"Failed to load pipeline: {e}")
            raise
    
    def _load_ip_adapter(self):
        """加载IP-Adapter（如果可用）"""
        try:
            # 注意：这里需要根据实际的IP-Adapter实现进行调整
            # 由于IP-Adapter可能需要特定的权重文件，这里提供基础框架
            logger.info("IP-Adapter functionality would be loaded here")
            # self.pipeline.load_ip_adapter("h94/IP-Adapter", weight_name="ip-adapter_sd15.bin")
            # self.pipeline.set_ip_adapter_scale(self.ip_adapter_scale)
        except Exception as e:
            logger.warning(f"IP-Adapter not available: {e}")
    
    def generate_with_image_prompt(
        self,
        text_prompt: str,
        image_prompt: Union[str, Image.Image],
        negative_prompt: str = None,
        width: int = 512,
        height: int = 768,
        num_inference_steps: int = 25,
        guidance_scale: float = 7.5,
        seed: Optional[int] = None
    ) -> Image.Image:
        """
        使用图像提示和文本提示生成图像
        
        Args:
            text_prompt: 文本提示
            image_prompt: 图像提示（路径或PIL Image对象）
            negative_prompt: 负面提示
            width: 生成图像宽度
            height: 生成图像高度
            num_inference_steps: 推理步数
            guidance_scale: 引导强度
            seed: 随机种子
            
        Returns:
            生成的图像
        """
        if self.pipeline is None:
            raise RuntimeError("Pipeline not loaded")
        
        # 处理图像提示
        if isinstance(image_prompt, str):
            if not os.path.exists(image_prompt):
                raise FileNotFoundError(f"Image prompt file not found: {image_prompt}")
            image_prompt = Image.open(image_prompt).convert('RGB')
        
        # 设置随机种子
        if seed is not None:
            torch.manual_seed(seed)
        
        # 设置默认负面提示
        if negative_prompt is None:
            negative_prompt = (
                "EasyNegative, lowres, bad anatomy, text, cropped, low quality, "
                "(mutation, poorly drawn :1.2), normal quality, obesity, bad proportions, "
                "unnatural body, bad shadow, uncoordinated body, worst quality, censored, "
                "low quality, signature, watermark, username, blurry, nsfw"
            )
        
        try:
            logger.info(f"Generating image with text: '{text_prompt[:50]}...'")
            
            # 由于IP-Adapter可能不可用，这里使用标准的文本到图像生成
            # 在实际实现中，如果IP-Adapter可用，可以传入image_prompt
            result = self.pipeline(
                prompt=text_prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
                # ip_adapter_image=image_prompt,  # 如果IP-Adapter可用
            )
            
            generated_image = result.images[0]
            logger.info("Image generated successfully")
            
            return generated_image
            
        except Exception as e:
            logger.error(f"Failed to generate image: {e}")
            raise
    
    def generate_text_only(
        self,
        text_prompt: str,
        negative_prompt: str = None,
        width: int = 512,
        height: int = 768,
        num_inference_steps: int = 25,
        guidance_scale: float = 7.5,
        seed: Optional[int] = None
    ) -> Image.Image:
        """
        仅使用文本提示生成图像
        
        Args:
            text_prompt: 文本提示
            negative_prompt: 负面提示
            width: 生成图像宽度
            height: 生成图像高度
            num_inference_steps: 推理步数
            guidance_scale: 引导强度
            seed: 随机种子
            
        Returns:
            生成的图像
        """
        if self.pipeline is None:
            raise RuntimeError("Pipeline not loaded")
        
        # 设置随机种子
        if seed is not None:
            torch.manual_seed(seed)
        
        # 设置默认负面提示
        if negative_prompt is None:
            negative_prompt = (
                "EasyNegative, lowres, bad anatomy, text, cropped, low quality, "
                "(mutation, poorly drawn :1.2), normal quality, obesity, bad proportions, "
                "unnatural body, bad shadow, uncoordinated body, worst quality, censored, "
                "low quality, signature, watermark, username, blurry, nsfw"
            )
        
        try:
            logger.info(f"Generating image with text only: '{text_prompt[:50]}...'")
            
            result = self.pipeline(
                prompt=text_prompt,
                negative_prompt=negative_prompt,
                width=width,
                height=height,
                num_inference_steps=num_inference_steps,
                guidance_scale=guidance_scale,
            )
            
            generated_image = result.images[0]
            logger.info("Image generated successfully")
            
            return generated_image
            
        except Exception as e:
            logger.error(f"Failed to generate image: {e}")
            raise
    
    def set_ip_adapter_scale(self, scale: float):
        """
        设置IP-Adapter影响权重
        
        Args:
            scale: 权重值（0.0-1.0）
        """
        self.ip_adapter_scale = max(0.0, min(1.0, scale))
        logger.info(f"IP-Adapter scale set to {self.ip_adapter_scale}")
        
        # 如果IP-Adapter已加载，更新权重
        try:
            if hasattr(self.pipeline, 'set_ip_adapter_scale'):
                self.pipeline.set_ip_adapter_scale(self.ip_adapter_scale)
        except Exception as e:
            logger.warning(f"Failed to update IP-Adapter scale: {e}")
    
    def get_generator_info(self) -> dict:
        """
        获取生成器信息
        
        Returns:
            生成器信息字典
        """
        return {
            "model_path": self.model_path,
            "device": str(self.device),
            "pipeline_loaded": self.pipeline is not None,
            "ip_adapter_scale": self.ip_adapter_scale,
            "torch_dtype": str(self.pipeline.dtype) if self.pipeline else "Unknown"
        }


def main():
    """测试函数"""
    try:
        # 初始化生成器
        generator = IPAdapterGenerator()
        
        # 显示生成器信息
        info = generator.get_generator_info()
        print("Generator Info:", info)
        
        # 测试文本生成
        test_prompt = "传统中国花纹，精美刺绣，高质量，详细"
        print(f"\nGenerating image with prompt: '{test_prompt}'")
        
        image = generator.generate_text_only(
            text_prompt=test_prompt,
            width=512,
            height=512,
            num_inference_steps=20
        )
        
        # 保存测试图像
        output_dir = "src/ai_workflow/output/image"
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, "test_generation.png")
        image.save(output_path)
        print(f"Test image saved to: {output_path}")
        
    except Exception as e:
        print(f"Test failed: {e}")


if __name__ == "__main__":
    main()
