# 基于CLIP与IP-Adapter的图像检索增强生成模块

本模块实现了基于CLIP模型的图像检索和IP-Adapter增强的图像生成功能，能够根据文本描述检索相关图像，并将检索到的图像与文本提示结合生成新的图像。

## 功能特性

- **CLIP图像嵌入**: 使用CLIP模型对图像进行特征提取和嵌入
- **FAISS索引**: 构建高效的向量索引用于快速检索
- **文本图像检索**: 根据文本描述检索相似图像
- **IP-Adapter增强生成**: 结合检索图像和文本提示生成新图像
- **批量处理**: 支持批量图像生成
- **配置化管理**: 通过配置文件管理各种参数

## 目录结构

```
src/ai_workflow/preprocess/image_preprocessor/
├── __init__.py                     # 模块初始化
├── clip_image_indexer.py          # CLIP图像索引器
├── text_image_retriever.py        # 文本图像检索器
├── ip_adapter_generator.py        # IP-Adapter生成器
├── enhanced_image_generator.py    # 主要的增强生成器
├── test_enhanced_generator.py     # 测试脚本
├── install_dependencies.py        # 依赖安装脚本
├── README.md                      # 说明文档
├── faiss_index.index             # FAISS索引文件（运行时生成）
├── image_paths.json              # 图像路径映射（运行时生成）
└── clip_embeddings.npy           # CLIP嵌入向量（运行时生成）
```

## 安装依赖

运行依赖安装脚本：

```bash
cd src/ai_workflow/preprocess/image_preprocessor
python install_dependencies.py
```

或手动安装：

```bash
pip install torch torchvision transformers diffusers accelerate faiss-cpu Pillow numpy safetensors
```

## 模型准备

确保以下模型文件已放置在正确位置：

1. **CLIP模型**: `src/ai_workflow/models/clip-vit-base-patch16/`
   - config.json
   - pytorch_model.bin
   - tokenizer相关文件

2. **Stable Diffusion模型**: `src/ai_workflow/models/anything-v5/`
   - anything-v5.safetensors

## 使用方法

### 1. 基础使用

```python
from src.ai_workflow.preprocess.image_preprocessor import EnhancedImageGenerator

# 初始化生成器
generator = EnhancedImageGenerator(
    similarity_threshold=0.2,  # 相似度阈值
    ip_adapter_scale=0.5,      # IP-Adapter影响权重
    auto_build_index=True      # 自动构建索引
)

# 生成图像
image, info = generator.generate_enhanced_image(
    text_prompt="传统中国花纹，精美刺绣，高质量",
    width=512,
    height=768,
    use_image_enhancement=True
)

# 保存图像
image.save("output.png")
```

### 2. 通过生成器适配器使用

```python
from src.ai_workflow.generators.enhanced_image_generator import EnhancedImageGenerator

# 初始化适配器
adapter = EnhancedImageGenerator()

# 生成图像（兼容现有接口）
result = adapter.generate_image(
    prompt="传统花纹",
    image_name="test_image",
    mode="character"
)

print(result["status"])  # "success" 或 "error"
```

### 3. 搜索相似图像

```python
# 搜索相似图像
search_result = adapter.search_similar_images("传统花纹", top_k=5)

for result in search_result["results"]:
    print(f"图像: {result['image_name']}, 相似度: {result['similarity_score']:.3f}")
```

### 4. 批量生成

```python
prompts = [
    "传统中国花纹，精美刺绣",
    "古代建筑，宫殿风格",
    "文化遗产，历史文物"
]

results = generator.batch_generate(prompts, mode="character")
```

## 配置说明

### 预处理器配置 (config_preprocessor.json)

```json
{
  "image_preprocessor": {
    "clip_model_path": "src/ai_workflow/models/clip-vit-base-patch16",
    "sd_model_path": "src/ai_workflow/models/anything-v5",
    "similarity_threshold": 0.2,
    "ip_adapter_scale": 0.5,
    "auto_build_index": true,
    "input_directory": "src/ai_workflow/input/image",
    "output_directory": "src/ai_workflow/output/image",
    "supported_formats": [".jpg", ".jpeg", ".png", ".bmp", ".tiff"],
    "generation_settings": {
      "default_width": 512,
      "default_height": 768,
      "num_inference_steps": 25,
      "guidance_scale": 7.5
    }
  }
}
```

### 生成器配置 (config_generators.json)

```json
{
  "image_generation": {
    "enhanced_generation": {
      "enabled": true,
      "similarity_threshold": 0.2,
      "ip_adapter_scale": 0.5,
      "use_image_enhancement": true,
      "generation_settings": {
        "width": 512,
        "height": 768,
        "num_inference_steps": 25,
        "guidance_scale": 7.5
      }
    }
  }
}
```

## 工作流程

1. **索引构建阶段**:
   - 扫描输入目录中的所有图像
   - 使用CLIP模型提取图像特征
   - 构建FAISS索引并保存到磁盘

2. **检索阶段**:
   - 对输入文本使用CLIP文本编码器
   - 在FAISS索引中搜索最相似的图像
   - 根据相似度阈值过滤结果

3. **生成阶段**:
   - 如果找到相似图像，使用IP-Adapter增强生成
   - 否则使用纯文本生成
   - 保存生成的图像到输出目录

## 测试

运行测试脚本验证功能：

```bash
cd src/ai_workflow/preprocess/image_preprocessor
python test_enhanced_generator.py
```

测试脚本会检查：
- 模型文件是否存在
- 索引构建是否正常
- 图像检索是否工作
- 图像生成是否成功

## 故障排除

### 常见问题

1. **模型文件未找到**
   - 确保CLIP和Stable Diffusion模型文件在正确位置
   - 检查文件权限

2. **内存不足**
   - 减少batch_size
   - 使用CPU模式（设置device="cpu"）
   - 启用模型CPU卸载

3. **CUDA错误**
   - 确保PyTorch CUDA版本与系统CUDA版本兼容
   - 尝试使用CPU模式

4. **索引构建失败**
   - 检查输入目录是否存在图像文件
   - 确保图像格式受支持
   - 检查磁盘空间

### 性能优化

1. **GPU加速**: 确保使用CUDA版本的PyTorch
2. **内存优化**: 启用attention_slicing和model_cpu_offload
3. **索引优化**: 对于大量图像，考虑使用更高效的FAISS索引类型

## API参考

详细的API文档请参考各模块的docstring。主要类和方法：

- `CLIPImageIndexer`: CLIP图像索引器
- `TextImageRetriever`: 文本图像检索器
- `IPAdapterGenerator`: IP-Adapter生成器
- `EnhancedImageGenerator`: 主要的增强生成器

## 更新日志

- v1.0.0: 初始版本，实现基础的CLIP检索和图像生成功能
