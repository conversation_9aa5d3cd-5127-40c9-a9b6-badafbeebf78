"""
测试预处理器功能
"""

import os
import sys
import tempfile
from PIL import Image

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

from src.ai_workflow.preprocess.material_preprocessor import MaterialPreprocessor, ImagePreprocessor


def create_test_files():
    """创建测试文件"""
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="test_preprocess_")
    print(f"Created test directory: {test_dir}")
    
    # 创建测试文本文件
    text_file = os.path.join(test_dir, "test.txt")
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write("这是一个测试文本文件。\n包含一些中文内容用于测试预处理功能。")
    
    # 创建测试图像文件
    image_file = os.path.join(test_dir, "test.png")
    # 创建一个简单的测试图像
    img = Image.new('RGB', (100, 100), color='red')
    img.save(image_file)
    
    # 创建另一个图像文件
    image_file2 = os.path.join(test_dir, "test2.jpg")
    img2 = Image.new('RGB', (200, 150), color='blue')
    img2.save(image_file2)
    
    return test_dir, [text_file, image_file, image_file2]


def test_image_preprocessor():
    """测试图像预处理器"""
    print("\n" + "="*50)
    print("Testing Image Preprocessor")
    print("="*50)
    
    try:
        # 创建测试文件
        test_dir, test_files = create_test_files()
        image_files = [f for f in test_files if f.endswith(('.png', '.jpg'))]
        
        # 初始化图像预处理器
        preprocessor = ImagePreprocessor()
        
        # 显示配置信息
        print(f"Input directory: {preprocessor.input_dir}")
        print(f"Output directory: {preprocessor.output_dir}")
        
        # 测试单个图像处理
        print(f"\nTesting single image processing...")
        if image_files:
            result = preprocessor.copy_and_rename_image(image_files[0], 1)
            print(f"Processed: {result}")
        
        # 测试批量图像处理
        print(f"\nTesting batch image processing...")
        results = preprocessor.process_images(image_files)
        print(f"Processed {len(results)} images:")
        for result in results:
            print(f"  - {result}")
        
        # 测试目录处理
        print(f"\nTesting directory processing...")
        dir_results = preprocessor.process_directory(test_dir)
        print(f"Directory processing results: {len(dir_results)} images")
        
        # 清理
        import shutil
        shutil.rmtree(test_dir)
        print(f"Cleaned up test directory: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"Image preprocessor test failed: {e}")
        return False


def test_material_preprocessor():
    """测试素材预处理器"""
    print("\n" + "="*50)
    print("Testing Material Preprocessor")
    print("="*50)
    
    try:
        # 创建测试文件
        test_dir, test_files = create_test_files()
        
        # 初始化素材预处理器
        preprocessor = MaterialPreprocessor()
        
        # 显示配置信息
        info = preprocessor.get_output_info()
        print("Output directories:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 测试文件分类
        print(f"\nTesting file classification...")
        text_files, image_files = preprocessor.classify_files(test_files)
        print(f"Text files: {len(text_files)}")
        for f in text_files:
            print(f"  - {os.path.basename(f)}")
        print(f"Image files: {len(image_files)}")
        for f in image_files:
            print(f"  - {os.path.basename(f)}")
        
        # 测试目录分类
        print(f"\nTesting directory classification...")
        dir_text, dir_image = preprocessor.classify_directory(test_dir)
        print(f"Directory text files: {len(dir_text)}")
        print(f"Directory image files: {len(dir_image)}")
        
        # 测试批量处理
        print(f"\nTesting batch processing...")
        results = preprocessor.process_materials(test_files)
        print(f"Processing results:")
        print(f"  Text files: {len(results['text_files'])}")
        print(f"  Image files: {len(results['image_files'])}")
        
        # 测试目录处理
        print(f"\nTesting directory processing...")
        dir_results = preprocessor.process_directory(test_dir)
        print(f"Directory processing results:")
        print(f"  Text files: {len(dir_results['text_files'])}")
        print(f"  Image files: {len(dir_results['image_files'])}")
        
        # 清理
        import shutil
        shutil.rmtree(test_dir)
        print(f"Cleaned up test directory: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"Material preprocessor test failed: {e}")
        return False


def test_integration():
    """测试集成功能"""
    print("\n" + "="*50)
    print("Testing Integration")
    print("="*50)
    
    try:
        # 创建一个包含多种文件的测试目录
        test_dir = tempfile.mkdtemp(prefix="test_integration_")
        
        # 创建子目录
        sub_dir = os.path.join(test_dir, "subdir")
        os.makedirs(sub_dir)
        
        # 创建各种类型的文件
        files_created = []
        
        # 文本文件
        for i in range(3):
            text_file = os.path.join(test_dir if i < 2 else sub_dir, f"text_{i}.txt")
            with open(text_file, 'w', encoding='utf-8') as f:
                f.write(f"测试文本文件 {i}\n内容...")
            files_created.append(text_file)
        
        # 图像文件
        for i in range(4):
            img_file = os.path.join(test_dir if i < 3 else sub_dir, f"image_{i}.png")
            img = Image.new('RGB', (50 + i*10, 50 + i*10), color=(i*60, 100, 200))
            img.save(img_file)
            files_created.append(img_file)
        
        print(f"Created {len(files_created)} test files in {test_dir}")
        
        # 使用素材预处理器处理整个目录
        preprocessor = MaterialPreprocessor()
        results = preprocessor.process_directory(test_dir)
        
        print(f"Integration test results:")
        print(f"  Processed text files: {len(results['text_files'])}")
        print(f"  Processed image files: {len(results['image_files'])}")
        
        # 验证输出文件是否存在
        for text_file in results['text_files']:
            if os.path.exists(text_file):
                print(f"  ✓ Text output exists: {os.path.basename(text_file)}")
            else:
                print(f"  ✗ Text output missing: {text_file}")
        
        for image_file in results['image_files']:
            if os.path.exists(image_file):
                print(f"  ✓ Image output exists: {os.path.basename(image_file)}")
            else:
                print(f"  ✗ Image output missing: {image_file}")
        
        # 清理
        import shutil
        shutil.rmtree(test_dir)
        print(f"Cleaned up test directory: {test_dir}")
        
        return True
        
    except Exception as e:
        print(f"Integration test failed: {e}")
        return False


def main():
    """主测试函数"""
    print("Material Preprocessor Test Suite")
    print("="*50)
    
    tests = [
        ("Image Preprocessor", test_image_preprocessor),
        ("Material Preprocessor", test_material_preprocessor),
        ("Integration Test", test_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "="*50)
    print("Test Summary")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✓ PASSED" if result else "✗ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Preprocessors are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == len(results)


if __name__ == "__main__":
    main()
