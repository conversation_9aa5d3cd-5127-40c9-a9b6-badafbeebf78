# 📁 AI-GAL 标准项目结构

## 🎯 项目结构概览

```
AI-GAL/
├── 🚀 主入口
│   └── launcher.py                   # 主启动器
│
├── 📦 源代码 (src/)
│   ├── core/                         # 核心模块
│   │   ├── __init__.py
│   │   ├── material_preprocessor.py  # 素材预处理
│   │   ├── rag_retrieval.py         # RAG检索系统
│   │   └── main_pipeline_controller.py # 主流程控制
│   │
│   ├── generators/                   # 生成器模块
│   │   ├── __init__.py
│   │   ├── game_framework_generator.py # 游戏框架生成
│   │   ├── node_narrative_generator.py # 节点叙事生成
│   │   ├── image_generator.py        # 图像生成
│   │   ├── audio_generator.py        # 音频生成
│   │   ├── renpy_integrator.py      # Ren'Py整合
│   │   └── ui_design_module.py      # UI设计
│   │
│   └── utils/                        # 工具模块
│       ├── __init__.py
│       └── gpt_client.py            # GPT API客户端
│
├── ⚙️ 配置 (config/)
│   ├── __init__.py
│   ├── config.ini                   # 系统配置
│   ├── requirements.txt             # 依赖包列表
│   └── prompts/                     # 提示词库
│       ├── __init__.py
│       ├── framework_prompts.py     # 游戏框架提示词
│       ├── narrative_prompts.py     # 节点叙事提示词
│       ├── image_prompts.py         # 图像生成提示词
│       └── audio_prompts.py         # 音频生成提示词
│
├── 🧪 测试 (tests/)
│   ├── __init__.py
│   └── test_system.py               # 系统测试
│
├── 📚 文档 (docs/)
│   ├── __init__.py
│   └── 项目结构说明.md              # 本文档
│
├── 🔧 基础组件 (保持原位置)
│   ├── local_image_generator.py     # 本地图像生成
│   ├── cloud_image_generator.py     # 云端图像生成
│   ├── local_vocal_generator.py     # 本地语音生成
│   ├── cloud_vocal_generator.py     # 云端语音生成
│   └── music_generator.py           # 音乐生成
│
└── 📂 数据目录 (运行时生成)
    ├── data/                        # 处理数据
    │   ├── SAD/                     # 汇总文档
    │   ├── VSI/                     # 视觉语义库存
    │   ├── framework/               # 游戏框架
    │   ├── narratives/              # 节点叙事
    │   └── vector_db/               # 向量数据库
    │
    └── output/                      # 输出结果
        └── generated_game/          # 最终游戏
```

## 🎨 设计原则

### 1. 标准Python项目结构
- **src/** 目录包含所有源代码
- **config/** 目录包含配置文件
- **tests/** 目录包含测试代码
- **docs/** 目录包含文档

### 2. 模块化分层
- **core/** 核心业务逻辑
- **generators/** 各种生成器
- **utils/** 通用工具函数

### 3. 配置集中管理
- 所有配置文件在 **config/** 目录
- 提示词独立管理便于工程化

### 4. 清晰的数据流
- **data/** 存储中间处理数据
- **output/** 存储最终生成结果

## 🚀 使用方式

### 快速启动
```bash
# 安装依赖
pip install -r config/requirements.txt
python -m spacy download zh_core_web_sm

# 配置系统
# 编辑 config/config.ini

# 启动系统
python launcher.py
```

### 开发模式
```bash
# 运行测试
python tests/test_system.py

# 或通过启动器
python launcher.py
# 选择 "运行系统测试"
```

## 🔧 模块说明

### 核心模块 (src/core/)
- **material_preprocessor.py**: 处理输入素材，生成SAD和VSI
- **rag_retrieval.py**: 构建和查询向量数据库
- **main_pipeline_controller.py**: 统一流程控制

### 生成器模块 (src/generators/)
- **game_framework_generator.py**: 生成游戏框架和角色设定
- **node_narrative_generator.py**: 为每个节点生成详细叙事
- **image_generator.py**: 生成游戏所需图像
- **audio_generator.py**: 生成音频和音乐
- **renpy_integrator.py**: 整合为Ren'Py脚本
- **ui_design_module.py**: 生成UI界面和交互逻辑

### 工具模块 (src/utils/)
- **gpt_client.py**: 统一的GPT API调用接口

### 配置模块 (config/)
- **config.ini**: 系统主配置文件
- **requirements.txt**: Python依赖包列表
- **prompts/**: 提示词库，支持提示词工程

## 📊 数据流程

```
原始素材 → 素材预处理 → RAG索引 → 游戏框架 → 节点叙事 → 多媒体生成 → Ren'Py整合 → 完整游戏
    ↓           ↓          ↓         ↓         ↓          ↓           ↓          ↓
   文本/图像    SAD/VSI    向量库    剧情骨架   详细剧本    图像/音频    .rpy脚本   可运行游戏
```

## 🎯 优势特点

### 1. 标准化结构
- 符合Python项目最佳实践
- 便于团队协作和维护
- 支持标准的包管理

### 2. 模块化设计
- 清晰的职责分工
- 易于单独测试和调试
- 支持功能扩展

### 3. 配置工程化
- 集中的配置管理
- 独立的提示词库
- 便于版本控制

### 4. 数据管理
- 清晰的数据存储结构
- 分离的中间数据和最终输出
- 便于数据追踪和调试

## 🔮 扩展指南

### 添加新的生成器
1. 在 `src/generators/` 创建新模块
2. 在 `config/prompts/` 添加对应提示词
3. 在 `src/core/main_pipeline_controller.py` 中集成

### 添加新的配置
1. 在 `config/config.ini` 添加配置项
2. 在相关模块中读取配置
3. 更新文档说明

### 添加新的测试
1. 在 `tests/` 目录添加测试文件
2. 在 `tests/test_system.py` 中集成
3. 通过启动器运行测试

---

这个标准化的项目结构使AI-GAL更加专业、易维护，符合现代Python项目的最佳实践。
