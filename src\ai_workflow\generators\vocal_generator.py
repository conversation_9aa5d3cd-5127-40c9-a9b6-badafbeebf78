"""
统一的语音生成器
整合云端和本地语音生成功能，根据配置自动选择生成方式
"""

import os
from typing import Dict, List, Optional, Union
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

# 导入具体的生成器实现
from .cloud_vocal_generator import online_generate_audio
from .local_vocal_generator import generate_audio

class VocalGenerator:
    """统一的语音生成器类"""
    
    def __init__(self):
        """初始化语音生成器"""
        # 获取语音生成配置
        self.vocal_config = get_section('generators', 'vocal_generation')

        # 从配置文件读取是否使用云端生成
        self.use_cloud = self.vocal_config.get('use_cloud', False)

        # 语音输出目录
        output_dir = self.vocal_config.get('output_directory', 'audio')
        self.audio_directory = os.path.join(game_directory, output_dir)
        os.makedirs(self.audio_directory, exist_ok=True)

        # 获取SOVITS版本信息
        self.sovits_version = self.vocal_config.get('sovits_version', 'V1')

        print(f"语音生成器初始化完成，使用{'云端' if self.use_cloud else '本地'}生成")
        print(f"SOVITS版本: {self.sovits_version}")
    
    def generate_voice(self, content: str, speaker: Union[int, str], output_name: str) -> str:
        """
        生成单段语音
        
        Args:
            content: 要合成的文本内容
            speaker: 说话人ID或名称
            output_name: 输出音频文件名（不含扩展名）
            
        Returns:
            生成结果状态："ok"表示成功，"error"表示失败
        """
        try:
            if self.use_cloud:
                print(f"使用云端生成语音: {output_name}")
                result = online_generate_audio(content, speaker, output_name)
                return "ok" if result is None else "error"
            else:
                print(f"使用本地生成语音: {output_name}")
                # 本地生成需要额外的参数
                speaker_id = self._convert_speaker_to_id(speaker)
                return generate_audio(self.sovits_version, content, speaker_id, output_name)
                
        except Exception as e:
            print(f"语音生成失败: {e}")
            return "error"
    
    def generate_audio_for_node(self, narrative_data: Dict) -> Dict:
        """
        为单个节点生成所需的所有语音
        
        Args:
            narrative_data: 节点叙事数据，包含对话、旁白等
            
        Returns:
            包含生成语音信息的字典
        """
        node_id = narrative_data.get("node_id", "unknown")
        narrative_content = narrative_data.get("narrative_content", {})
        
        result = {
            "node_id": node_id,
            "voice_files": [],
            "music_files": [],
            "sound_files": [],
            "generation_status": "success"
        }
        
        try:
            # 生成对话语音
            dialogues = narrative_content.get("dialogues", [])
            for i, dialogue in enumerate(dialogues):
                character_name = dialogue.get("character", "narrator")
                content = dialogue.get("content", "")
                
                if content.strip():
                    voice_name = f"voice_{node_id}_{character_name}_{i+1}"
                    speaker_id = self._get_speaker_for_character(character_name)
                    status = self.generate_voice(content, speaker_id, voice_name)
                    
                    result["voice_files"].append({
                        "character": character_name,
                        "content": content,
                        "file_name": voice_name,
                        "file_path": f"audio/{voice_name}.wav",
                        "status": status
                    })
            
            # 生成旁白语音
            narrations = narrative_content.get("narrations", [])
            for i, narration in enumerate(narrations):
                if narration.strip():
                    narrator_voice_name = f"narrator_{node_id}_{i+1}"
                    status = self.generate_voice(narration, "narrator", narrator_voice_name)
                    
                    result["voice_files"].append({
                        "character": "narrator",
                        "content": narration,
                        "file_name": narrator_voice_name,
                        "file_path": f"audio/{narrator_voice_name}.wav",
                        "status": status
                    })
            
            # 处理音乐指令（这里只记录，实际音乐生成由music_generator处理）
            music_instructions = narrative_content.get("music_instructions", [])
            for music_inst in music_instructions:
                result["music_files"].append({
                    "instruction": music_inst,
                    "status": "pending"  # 待音乐生成器处理
                })
                
        except Exception as e:
            print(f"节点 {node_id} 语音生成失败: {e}")
            result["generation_status"] = "error"
            result["error_message"] = str(e)
        
        return result
    
    def generate_character_voices(self, characters: List[Dict], sample_texts: List[str]) -> List[Dict]:
        """
        为角色生成示例语音
        
        Args:
            characters: 角色信息列表
            sample_texts: 示例文本列表
            
        Returns:
            生成结果列表
        """
        results = []
        
        for char_info in characters:
            char_name = char_info.get("name", "unknown")
            
            for i, sample_text in enumerate(sample_texts):
                voice_name = f"sample_{char_name}_{i+1}"
                speaker_id = self._get_speaker_for_character(char_name)
                status = self.generate_voice(sample_text, speaker_id, voice_name)
                
                results.append({
                    "character_name": char_name,
                    "sample_text": sample_text,
                    "file_name": voice_name,
                    "file_path": f"audio/{voice_name}.wav",
                    "status": status
                })
        
        return results
    
    def _convert_speaker_to_id(self, speaker: Union[int, str]) -> int:
        """
        将说话人转换为ID
        
        Args:
            speaker: 说话人ID或名称
            
        Returns:
            说话人ID
        """
        if isinstance(speaker, int):
            return speaker
        
        # 根据角色名称映射到ID
        speaker_mapping = {
            "narrator": 1,
            "杰帕德": 1,
            "三月七": 2,
            "克拉拉": 3,
            "符玄": 4,
            "青雀": 5,
            "黑塔": 6
        }
        
        return speaker_mapping.get(speaker, 1)
    
    def _get_speaker_for_character(self, character_name: str) -> int:
        """
        根据角色名称获取对应的说话人ID
        
        Args:
            character_name: 角色名称
            
        Returns:
            说话人ID
        """
        # 这里可以根据角色设定来分配不同的声音
        # 暂时使用简单的映射
        character_voice_mapping = {
            "林小满": 2,  # 三月七
            "祖母": 4,    # 符玄
            "村民": 1,    # 杰帕德
            "narrator": 6  # 黑塔作为旁白
        }
        
        return character_voice_mapping.get(character_name, 1)
    
    def switch_generation_mode(self, use_cloud: bool):
        """
        切换生成模式（云端/本地）
        
        Args:
            use_cloud: True为云端生成，False为本地生成
        """
        self.use_cloud = use_cloud
        print(f"语音生成模式已切换为: {'云端' if use_cloud else '本地'}")
    
    def get_generation_status(self) -> Dict:
        """
        获取生成器状态信息
        
        Returns:
            包含状态信息的字典
        """
        return {
            "mode": "cloud" if self.use_cloud else "local",
            "sovits_version": self.sovits_version,
            "output_directory": self.audio_directory,
            "available": self._check_availability()
        }
    
    def _check_availability(self) -> bool:
        """
        检查生成器可用性

        Returns:
            True表示可用，False表示不可用
        """
        try:
            if self.use_cloud:
                # 检查云端配置
                api_key = self.vocal_config.get('cloud_api_key', '')
                return bool(api_key.strip())
            else:
                # 检查本地服务
                import requests
                local_url = self.vocal_config.get('local_url', 'http://127.0.0.1:9880')
                response = requests.get(local_url, timeout=5)
                return response.status_code == 200
        except:
            return False


# 为了保持向后兼容性，提供便捷函数
def generate_voice_unified(content: str, speaker: Union[int, str], output_name: str) -> str:
    """
    统一的语音生成函数（向后兼容）
    
    Args:
        content: 要合成的文本内容
        speaker: 说话人ID或名称
        output_name: 输出音频文件名
        
    Returns:
        生成结果状态
    """
    generator = VocalGenerator()
    return generator.generate_voice(content, speaker, output_name)


if __name__ == "__main__":
    # 测试代码
    generator = VocalGenerator()
    print(f"生成器状态: {generator.get_generation_status()}")
    
    # 测试生成
    test_result = generator.generate_voice(
        "这是一个测试语音",
        2,
        "test_voice"
    )
    print(f"测试生成结果: {test_result}")
