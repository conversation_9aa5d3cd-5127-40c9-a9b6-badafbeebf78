"""
AI-GAL 后端服务器
基于FastAPI的后端服务启动器
"""

import os
import sys
import uvicorn
from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from src.backend.router import router

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    print("🚀 AI-GAL 后端服务启动中...")
    print(f"📁 项目根目录: {project_root}")
    
    # 检查关键目录和文件
    config_path = os.path.join(project_root, "config.ini")
    if os.path.exists(config_path):
        print("✅ 配置文件已找到")
    else:
        print("⚠️  配置文件未找到，请检查 config.ini")
    
    print("✅ 后端服务启动完成")
    
    yield
    
    # 关闭时执行
    print("🛑 AI-GAL 后端服务关闭")

# 创建FastAPI应用
app = FastAPI(
    title="AI-GAL Backend API",
    description="AI-GAL 文化遗产游戏生成系统后端API",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体的域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加路由
app.include_router(router, prefix="/api/v1")

# 全局异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理器"""
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": f"服务器内部错误: {str(exc)}",
            "data": None,
            "path": str(request.url)
        }
    )

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "name": "AI-GAL Backend API",
        "version": "1.0.0",
        "description": "AI-GAL 文化遗产游戏生成系统后端API",
        "status": "running",
        "docs": "/docs",
        "api_prefix": "/api/v1"
    }

# 健康检查
@app.get("/health")
async def health():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "AI-GAL Backend",
        "version": "1.0.0"
    }

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI-GAL 后端服务器")
    parser.add_argument("--host", default="127.0.0.1", help="服务器地址")
    parser.add_argument("--port", type=int, default=8080, help="服务器端口")
    parser.add_argument("--reload", action="store_true", help="开启自动重载")
    parser.add_argument("--workers", type=int, default=1, help="工作进程数")
    
    args = parser.parse_args()
    
    print(f"""
╔══════════════════════════════════════════════════════════════╗
║                    AI-GAL 后端服务器                         ║
║                        版本 1.0.0                           ║
║                                                              ║
║  提供GUI调用的后端API接口                                    ║
║  支持同步和异步服务调用                                      ║
╚══════════════════════════════════════════════════════════════╝

🌐 服务地址: http://{args.host}:{args.port}
📚 API文档: http://{args.host}:{args.port}/docs
🔧 管理界面: http://{args.host}:{args.port}/redoc
""")
    
    # 启动服务器
    uvicorn.run(
        "backend.server:app",
        host=args.host,
        port=args.port,
        reload=args.reload,
        workers=args.workers,
        log_level="info"
    )

if __name__ == "__main__":
    main()
