"""
统一的图像生成器
整合云端和本地图像生成功能，根据配置自动选择生成方式
"""

import os
from typing import Dict, List, Optional, Union
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

# 导入具体的生成器实现
from .cloud_image_generator import online_generate_image
from .local_image_generator import generate_image

class ImageGenerator:
    """统一的图像生成器类"""
    
    def __init__(self):
        """初始化图像生成器"""
        # 获取图像生成配置
        self.image_config = get_section('generators', 'image_generation')

        # 从配置文件读取是否使用云端生成
        self.use_cloud = self.image_config.get('use_cloud', False)

        # 图像输出目录
        output_dir = self.image_config.get('output_directory', 'images')
        self.images_directory = os.path.join(game_directory, output_dir)
        os.makedirs(self.images_directory, exist_ok=True)

        print(f"图像生成器初始化完成，使用{'云端' if self.use_cloud else '本地'}生成")
    
    def generate_image(self, prompt: str, image_name: str, mode: str = "background") -> str:
        """
        生成单张图像
        
        Args:
            prompt: 图像生成提示词
            image_name: 输出图像文件名（不含扩展名）
            mode: 生成模式，"background"为背景图，"character"为角色图
            
        Returns:
            生成结果状态："ok"表示成功，"error"表示失败
        """
        try:
            if self.use_cloud:
                print(f"使用云端生成图像: {image_name}")
                result = online_generate_image(prompt, image_name, mode)
                return "ok" if result is None else "error"
            else:
                print(f"使用本地生成图像: {image_name}")
                return generate_image(prompt, image_name, mode)
                
        except Exception as e:
            print(f"图像生成失败: {e}")
            return "error"
    
    def generate_images_for_node(self, narrative_data: Dict) -> Dict:
        """
        为单个节点生成所需的所有图像
        
        Args:
            narrative_data: 节点叙事数据，包含场景描述、角色信息等
            
        Returns:
            包含生成图像信息的字典
        """
        node_id = narrative_data.get("node_id", "unknown")
        narrative_content = narrative_data.get("narrative_content", {})
        
        result = {
            "node_id": node_id,
            "backgrounds": [],
            "characters": [],
            "cg_images": [],
            "generation_status": "success"
        }
        
        try:
            # 生成背景图像
            settings = narrative_content.get("settings", [])
            for i, setting in enumerate(settings):
                bg_name = f"bg_{node_id}_{i+1}"
                status = self.generate_image(setting, bg_name, "background")
                
                result["backgrounds"].append({
                    "name": bg_name,
                    "description": setting,
                    "file_path": f"images/{bg_name}.png",
                    "status": status
                })
            
            # 生成角色立绘
            characters = narrative_content.get("characters", [])
            for char_info in characters:
                char_name = char_info.get("name", "unknown")
                char_description = char_info.get("description", "")
                
                if char_description:
                    char_image_name = f"char_{char_name}_{node_id}"
                    status = self.generate_image(char_description, char_image_name, "character")
                    
                    result["characters"].append({
                        "character_name": char_name,
                        "image_name": char_image_name,
                        "description": char_description,
                        "file_path": f"images/{char_image_name}.png",
                        "status": status
                    })
            
            # 生成特殊CG图像（如果有标记）
            cg_descriptions = narrative_content.get("cg_scenes", [])
            for i, cg_desc in enumerate(cg_descriptions):
                cg_name = f"cg_{node_id}_{i+1}"
                status = self.generate_image(cg_desc, cg_name, "background")
                
                result["cg_images"].append({
                    "name": cg_name,
                    "description": cg_desc,
                    "file_path": f"images/{cg_name}.png",
                    "status": status
                })
                
        except Exception as e:
            print(f"节点 {node_id} 图像生成失败: {e}")
            result["generation_status"] = "error"
            result["error_message"] = str(e)
        
        return result
    
    def generate_character_portraits(self, characters: List[Dict]) -> List[Dict]:
        """
        批量生成角色立绘
        
        Args:
            characters: 角色信息列表
            
        Returns:
            生成结果列表
        """
        results = []
        
        for char_info in characters:
            char_name = char_info.get("name", "unknown")
            char_description = char_info.get("appearance_description", "")
            
            if char_description:
                image_name = f"portrait_{char_name}"
                status = self.generate_image(char_description, image_name, "character")
                
                results.append({
                    "character_name": char_name,
                    "image_name": image_name,
                    "description": char_description,
                    "file_path": f"images/{image_name}.png",
                    "status": status
                })
        
        return results
    
    def switch_generation_mode(self, use_cloud: bool):
        """
        切换生成模式（云端/本地）
        
        Args:
            use_cloud: True为云端生成，False为本地生成
        """
        self.use_cloud = use_cloud
        print(f"图像生成模式已切换为: {'云端' if use_cloud else '本地'}")
    
    def get_generation_status(self) -> Dict:
        """
        获取生成器状态信息
        
        Returns:
            包含状态信息的字典
        """
        return {
            "mode": "cloud" if self.use_cloud else "local",
            "output_directory": self.images_directory,
            "available": self._check_availability()
        }
    
    def _check_availability(self) -> bool:
        """
        检查生成器可用性

        Returns:
            True表示可用，False表示不可用
        """
        try:
            if self.use_cloud:
                # 检查云端配置
                api_key = self.image_config.get('cloud_api_key', '')
                return bool(api_key.strip())
            else:
                # 检查本地服务
                import requests
                local_url = self.image_config.get('local_url', 'http://127.0.0.1:7860')
                response = requests.get(local_url, timeout=5)
                return response.status_code == 200
        except:
            return False


# 为了保持向后兼容性，提供便捷函数
def generate_image_unified(prompt: str, image_name: str, mode: str = "background") -> str:
    """
    统一的图像生成函数（向后兼容）
    
    Args:
        prompt: 图像生成提示词
        image_name: 输出图像文件名
        mode: 生成模式
        
    Returns:
        生成结果状态
    """
    generator = ImageGenerator()
    return generator.generate_image(prompt, image_name, mode)


if __name__ == "__main__":
    # 测试代码
    generator = ImageGenerator()
    print(f"生成器状态: {generator.get_generation_status()}")
    
    # 测试生成
    test_result = generator.generate_image(
        "ancient Chinese temple in the rain, stone dog statue",
        "test_image",
        "background"
    )
    print(f"测试生成结果: {test_result}")
