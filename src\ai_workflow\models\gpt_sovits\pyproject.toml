[project]
name = "GPT-SoVITS-Infer"
version = "0.2.10"
description = "Inference code for GPT-SoVITS"
authors = [
    {name = "<PERSON><PERSON>",email = "<EMAIL>"},
]
dependencies = [
    "numpy>=1.26.4,<2",
    "scipy>=1.13.1",
    "librosa>=0.10.2.post1",
    "transformers>=4.41.2",
    "LangSegment>=0.3.3",
    "einops>=0.8.0",
    "matplotlib>=3.9.0",
    "cn2an>=0.5.22",
    "pypinyin>=0.51.0",
    "jieba-fast>=0.53",
    "pyopenjtalk>=0.3.3",
    "g2p-en>=2.1.0",
    "wordsegment>=1.3.1",
    "nltk>=3.8.1",
    "pydantic>=2.7.4",
]
requires-python = ">=3.10, <3.12"
readme = "README.md"
license = {text = "MIT"}

[project.optional-dependencies]
runpod = [
    "runpod~=1.6",
]
[build-system]
requires = ["pdm-backend"]
build-backend = "pdm.backend"


[tool.pdm]
distribution = true
