# AI-GAL 文化遗产游戏生成系统依赖包

# 核心依赖
requests>=2.28.0
configparser>=5.3.0

# 文本处理
spacy>=3.4.0
# 中文模型安装命令: python -m spacy download zh_core_web_sm

# 图像处理
Pillow>=9.0.0

# 数值计算和机器学习
numpy>=1.21.0
scikit-learn>=1.1.0

# PDF处理
PyPDF2>=3.0.0

#后端依赖
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
python-multipart==0.0.19

# 可选依赖（用于增强功能）

# 音频处理（可选）
# librosa>=0.9.0
# soundfile>=0.10.0

# 图像分析（可选）
# opencv-python>=4.5.0
# torch>=1.12.0
# torchvision>=0.13.0

# CLIP-Interrogator（可选，用于图像语义分析）
# clip-interrogator>=0.6.0

# 向量数据库（可选，用于更高级的RAG功能）
# faiss-cpu>=1.7.0
# chromadb>=0.3.0

# 开发和测试工具（可选）
# pytest>=7.0.0
# black>=22.0.0
# flake8>=4.0.0

# 注意事项：
# 1. spaCy中文模型需要单独安装：python -m spacy download zh_core_web_sm
# 2. 可选依赖根据需要安装，不是必需的
# 3. GPU相关的包（如torch）需要根据系统配置选择合适版本
# 4. 某些包可能需要额外的系统依赖
