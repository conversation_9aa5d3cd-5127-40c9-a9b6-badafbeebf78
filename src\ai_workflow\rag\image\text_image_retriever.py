"""
文本图像检索模块
使用CLIP模型对文本进行编码，在FAISS索引中检索相似图像
"""

import os
import numpy as np
import torch
from transformers import CLIPModel, CLIPProcessor
from typing import List, Tuple, Optional
import logging
from clip_image_indexer import CLIPImageIndexer

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextImageRetriever:
    """文本图像检索器"""
    
    def __init__(self, model_path: str = None, similarity_threshold: float = 0.2):
        """
        初始化文本图像检索器
        
        Args:
            model_path: CLIP模型路径，默认使用本地模型
            similarity_threshold: 相似度阈值，低于此值的检索结果将被过滤
        """
        # 设置模型路径
        if model_path is None:
            # 使用本地模型路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            model_path = os.path.join(current_dir, "../../models/clip-vit-base-patch16")
        
        self.model_path = model_path
        self.similarity_threshold = similarity_threshold
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 初始化模型和处理器
        self._load_model()
        
        # 初始化索引器
        self.indexer = CLIPImageIndexer(model_path)
        
        # 尝试加载已有索引
        if not self.indexer.load_index():
            logger.warning("No existing index found. Please build index first.")
    
    def _load_model(self):
        """加载CLIP模型和处理器"""
        try:
            logger.info(f"Loading CLIP model from {self.model_path}")
            self.model = CLIPModel.from_pretrained(self.model_path)
            self.processor = CLIPProcessor.from_pretrained(self.model_path)
            self.model.to(self.device)
            self.model.eval()
            logger.info("CLIP model loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load CLIP model: {e}")
            raise
    
    def extract_text_embedding(self, text: str) -> np.ndarray:
        """
        提取文本的CLIP嵌入
        
        Args:
            text: 输入文本
            
        Returns:
            文本嵌入向量
        """
        try:
            # 预处理文本
            inputs = self.processor(text=[text], return_tensors="pt", padding=True, truncation=True)
            inputs = {k: v.to(self.device) for k, v in inputs.items()}
            
            # 提取文本特征
            with torch.no_grad():
                text_features = self.model.get_text_features(**inputs)
                # 归一化特征向量
                text_features = text_features / text_features.norm(dim=-1, keepdim=True)
            
            return text_features.cpu().numpy().flatten()
        
        except Exception as e:
            logger.error(f"Failed to extract text embedding: {e}")
            return None
    
    def search_similar_images(self, text: str, top_k: int = 5) -> List[Tuple[str, float]]:
        """
        根据文本检索相似图像
        
        Args:
            text: 查询文本
            top_k: 返回前k个最相似的图像
            
        Returns:
            (图像路径, 相似度分数)的列表
        """
        if self.indexer.index is None:
            logger.error("No index loaded. Please build or load index first.")
            return []
        
        # 提取文本嵌入
        text_embedding = self.extract_text_embedding(text)
        if text_embedding is None:
            return []
        
        # 在索引中搜索
        text_embedding = text_embedding.reshape(1, -1).astype('float32')
        distances, indices = self.indexer.index.search(text_embedding, top_k)
        
        # 处理结果
        results = []
        for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
            if idx >= 0 and idx < len(self.indexer.image_paths):
                image_path = self.indexer.image_paths[idx]
                similarity = float(distance)  # 内积距离，值越大越相似
                
                # 应用相似度阈值过滤
                if similarity >= self.similarity_threshold:
                    results.append((image_path, similarity))
                    logger.info(f"Found similar image: {os.path.basename(image_path)} (similarity: {similarity:.3f})")
                else:
                    logger.info(f"Image {os.path.basename(image_path)} filtered out (similarity: {similarity:.3f} < {self.similarity_threshold})")
        
        return results
    
    def search_best_match(self, text: str) -> Optional[Tuple[str, float]]:
        """
        检索最佳匹配的图像
        
        Args:
            text: 查询文本
            
        Returns:
            最佳匹配的(图像路径, 相似度分数)，如果没有符合阈值的结果则返回None
        """
        results = self.search_similar_images(text, top_k=1)
        return results[0] if results else None
    
    def batch_search(self, texts: List[str], top_k: int = 5) -> List[List[Tuple[str, float]]]:
        """
        批量检索多个文本对应的相似图像
        
        Args:
            texts: 文本列表
            top_k: 每个文本返回前k个最相似的图像
            
        Returns:
            每个文本对应的检索结果列表
        """
        results = []
        for text in texts:
            text_results = self.search_similar_images(text, top_k)
            results.append(text_results)
        return results
    
    def update_similarity_threshold(self, threshold: float):
        """
        更新相似度阈值
        
        Args:
            threshold: 新的相似度阈值
        """
        self.similarity_threshold = threshold
        logger.info(f"Similarity threshold updated to {threshold}")
    
    def rebuild_index(self, image_dir: str) -> bool:
        """
        重新构建索引
        
        Args:
            image_dir: 图像目录路径
            
        Returns:
            是否成功重建
        """
        logger.info("Rebuilding index...")
        if self.indexer.build_index_from_directory(image_dir):
            return self.indexer.save_index()
        return False
    
    def get_retrieval_stats(self) -> dict:
        """
        获取检索统计信息
        
        Returns:
            检索统计信息
        """
        index_info = self.indexer.get_index_info()
        return {
            **index_info,
            "similarity_threshold": self.similarity_threshold,
            "device": str(self.device)
        }


def main():
    """测试函数"""
    # 初始化检索器
    retriever = TextImageRetriever(similarity_threshold=0.1)
    
    # 检查索引状态
    stats = retriever.get_retrieval_stats()
    print("Retrieval Stats:", stats)
    
    if stats.get("total_images", 0) == 0:
        print("No index found. Building index...")
        image_dir = "src/ai_workflow/input/image"
        if retriever.rebuild_index(image_dir):
            print("Index built successfully")
        else:
            print("Failed to build index")
            return
    
    # 测试检索
    test_queries = [
        "a photo of a cat",
        "a red embroidered traditional costume",
        "a cat in a red carpet",
        "dog",
        "panda"
    ]
    
    for query in test_queries:
        print(f"\nSearching for: '{query}'")
        results = retriever.search_similar_images(query, top_k=5)
        
        if results:
            for i, (image_path, similarity) in enumerate(results):
                print(f"  {i+1}. {os.path.basename(image_path)} (similarity: {similarity:.3f})")
        else:
            print("  No similar images found above threshold")


if __name__ == "__main__":
    main()
