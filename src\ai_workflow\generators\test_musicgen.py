#!/usr/bin/env python3
"""
MusicGen模型测试脚本
用于验证MusicGen模型是否能正常工作
"""

import os
import sys
import time

# 添加路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

def test_musicgen_dependencies():
    """测试MusicGen依赖是否安装"""
    print("=== 测试MusicGen依赖 ===")
    
    try:
        import torch
        print(f"✓ PyTorch版本: {torch.__version__}")
        print(f"✓ CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            print(f"✓ CUDA设备数量: {torch.cuda.device_count()}")
            print(f"✓ 当前CUDA设备: {torch.cuda.current_device()}")
    except ImportError:
        print("✗ PyTorch未安装")
        return False
    
    try:
        import transformers
        print(f"✓ Transformers版本: {transformers.__version__}")
    except ImportError:
        print("✗ Transformers未安装")
        return False
    
    try:
        import scipy
        print(f"✓ SciPy版本: {scipy.__version__}")
    except ImportError:
        print("✗ SciPy未安装")
        return False
    
    try:
        import numpy as np
        print(f"✓ NumPy版本: {np.__version__}")
    except ImportError:
        print("✗ NumPy未安装")
        return False
    
    return True

def test_musicgen_model():
    """测试MusicGen模型加载和推理"""
    print("\n=== 测试MusicGen模型 ===")
    
    try:
        import torch
        from transformers import MusicgenForConditionalGeneration, AutoProcessor
        import scipy.io.wavfile as wavfile
        import numpy as np
        
        # 使用较小的模型进行测试
        model_name = "facebook/musicgen-small"
        print(f"加载模型: {model_name}")
        
        # 检查设备
        device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"使用设备: {device}")
        
        # 加载处理器
        print("加载处理器...")
        processor = AutoProcessor.from_pretrained(model_name)
        print("✓ 处理器加载成功")
        
        # 加载模型
        print("加载模型...")
        model = MusicgenForConditionalGeneration.from_pretrained(
            model_name,
            torch_dtype=torch.float16 if device == "cuda" else torch.float32,
            device_map="auto" if device == "cuda" else None
        )
        print("✓ 模型加载成功")
        
        # 准备测试输入
        test_prompt = "peaceful piano music"
        print(f"测试提示词: {test_prompt}")
        
        inputs = processor(
            text=[test_prompt],
            padding=True,
            return_tensors="pt"
        )
        
        # 移动到设备
        if device == "cuda":
            inputs = {k: v.to(device) for k, v in inputs.items()}
            model = model.to(device)
        
        # 生成音乐（短时间测试）
        print("开始生成音乐...")
        start_time = time.time()
        
        with torch.no_grad():
            audio_values = model.generate(
                **inputs,
                max_new_tokens=512,  # 约15秒的音乐
                do_sample=True,
                guidance_scale=3.0,
                temperature=1.0
            )
        
        generation_time = time.time() - start_time
        print(f"✓ 音乐生成成功，耗时: {generation_time:.2f}秒")
        
        # 转换为numpy数组
        audio_data = audio_values[0, 0].cpu().numpy()
        sampling_rate = model.config.audio_encoder.sampling_rate
        
        print(f"✓ 音频数据形状: {audio_data.shape}")
        print(f"✓ 采样率: {sampling_rate}")
        print(f"✓ 音频长度: {len(audio_data) / sampling_rate:.2f}秒")
        
        # 保存测试音频
        output_dir = "test_output"
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, "test_musicgen.wav")
        
        # 标准化音频数据
        audio_data = np.clip(audio_data, -1.0, 1.0)
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        wavfile.write(output_path, sampling_rate, audio_int16)
        print(f"✓ 测试音频已保存: {output_path}")
        
        # 清理内存
        del model
        del processor
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return True
        
    except Exception as e:
        print(f"✗ MusicGen模型测试失败: {e}")
        return False

def test_music_generator():
    """测试MusicGenerator类"""
    print("\n=== 测试MusicGenerator类 ===")
    
    try:
        from music_generator import MusicGenerator
        
        # 创建生成器实例
        generator = MusicGenerator()
        print("✓ MusicGenerator实例创建成功")
        
        # 检查可用性
        status = generator.get_generation_status()
        print(f"✓ 生成器状态: {status}")
        
        # 测试本地可用性检查
        local_available = generator._check_local_availability()
        print(f"✓ 本地模型可用性: {local_available}")
        
        if local_available:
            # 测试音乐生成
            print("测试音乐生成...")
            result = generator.generate_music(
                "peaceful background music for game",
                "test_music",
                "common"
            )
            print(f"✓ 音乐生成结果: {result}")
        
        return True
        
    except Exception as e:
        print(f"✗ MusicGenerator测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("MusicGen模型测试开始")
    print("=" * 50)
    
    # 测试依赖
    if not test_musicgen_dependencies():
        print("\n依赖测试失败，请安装必要的库:")
        print("pip install torch transformers scipy numpy")
        return
    
    # 测试模型
    if not test_musicgen_model():
        print("\n模型测试失败")
        return
    
    # 测试生成器类
    if not test_music_generator():
        print("\n生成器类测试失败")
        return
    
    print("\n" + "=" * 50)
    print("✓ 所有测试通过！MusicGen模型工作正常")

if __name__ == "__main__":
    main()
