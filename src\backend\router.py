"""
API路由器
提供统一的HTTP API接口
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import asyncio
import uuid
from datetime import datetime

from .service_registry import call_service, get_available_services, get_service_info

# 创建路由器
router = APIRouter()

# 请求模型
class ServiceCallRequest(BaseModel):
    """服务调用请求模型"""
    service: str = Field(..., description="服务名称")
    params: Dict[str, Any] = Field(default_factory=dict, description="服务参数")

class AsyncServiceCallRequest(BaseModel):
    """异步服务调用请求模型"""
    service: str = Field(..., description="服务名称")
    params: Dict[str, Any] = Field(default_factory=dict, description="服务参数")
    callback_url: Optional[str] = Field(None, description="回调URL")

# 响应模型
class ServiceResponse(BaseModel):
    """服务响应模型"""
    status: str = Field(..., description="状态: success/error")
    message: str = Field(..., description="响应消息")
    data: Optional[Any] = Field(None, description="响应数据")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class AsyncTaskResponse(BaseModel):
    """异步任务响应模型"""
    task_id: str = Field(..., description="任务ID")
    status: str = Field(..., description="任务状态")
    message: str = Field(..., description="响应消息")
    timestamp: datetime = Field(default_factory=datetime.now, description="创建时间")

# 任务状态管理
_running_tasks: Dict[str, Dict[str, Any]] = {}

@router.get("/", response_model=Dict[str, Any])
async def root():
    """根路径，返回API信息"""
    return {
        "name": "AI-GAL Backend API",
        "version": "1.0.0",
        "description": "AI-GAL 文化遗产游戏生成系统后端API",
        "endpoints": {
            "/call": "同步服务调用",
            "/call-async": "异步服务调用",
            "/task/{task_id}": "查询异步任务状态",
            "/services": "获取所有可用服务",
            "/services/{service_name}": "获取特定服务信息",
            "/health": "健康检查"
        }
    }

@router.post("/call", response_model=ServiceResponse)
async def call_service_endpoint(request: ServiceCallRequest):
    """
    同步调用服务
    
    Args:
        request: 服务调用请求
        
    Returns:
        服务执行结果
    """
    try:
        result = call_service(request.service, **request.params)
        
        return ServiceResponse(
            status=result.get("status", "unknown"),
            message=result.get("message", ""),
            data=result.get("data")
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"服务调用失败: {str(e)}"
        )

@router.post("/call-async", response_model=AsyncTaskResponse)
async def call_service_async(request: AsyncServiceCallRequest, background_tasks: BackgroundTasks):
    """
    异步调用服务
    
    Args:
        request: 异步服务调用请求
        background_tasks: FastAPI后台任务
        
    Returns:
        任务创建响应
    """
    # 生成任务ID
    task_id = str(uuid.uuid4())
    
    # 初始化任务状态
    _running_tasks[task_id] = {
        "status": "pending",
        "service": request.service,
        "params": request.params,
        "callback_url": request.callback_url,
        "created_at": datetime.now(),
        "result": None,
        "error": None
    }
    
    # 添加后台任务
    background_tasks.add_task(
        _execute_async_service,
        task_id,
        request.service,
        request.params,
        request.callback_url
    )
    
    return AsyncTaskResponse(
        task_id=task_id,
        status="pending",
        message="任务已创建，正在执行中"
    )

@router.get("/task/{task_id}", response_model=Dict[str, Any])
async def get_task_status(task_id: str):
    """
    查询异步任务状态
    
    Args:
        task_id: 任务ID
        
    Returns:
        任务状态信息
    """
    if task_id not in _running_tasks:
        raise HTTPException(
            status_code=404,
            detail=f"任务 {task_id} 不存在"
        )
    
    task_info = _running_tasks[task_id]
    
    return {
        "task_id": task_id,
        "status": task_info["status"],
        "service": task_info["service"],
        "created_at": task_info["created_at"],
        "result": task_info.get("result"),
        "error": task_info.get("error")
    }

@router.get("/services", response_model=Dict[str, Any])
async def list_services():
    """
    获取所有可用服务列表
    
    Returns:
        服务列表
    """
    return get_available_services()

@router.get("/services/{service_name}", response_model=Dict[str, Any])
async def get_service_details(service_name: str):
    """
    获取特定服务的详细信息
    
    Args:
        service_name: 服务名称
        
    Returns:
        服务详细信息
    """
    result = get_service_info(service_name)
    
    if result["status"] == "error":
        raise HTTPException(
            status_code=404,
            detail=result["message"]
        )
    
    return result

@router.get("/health", response_model=Dict[str, Any])
async def health_check():
    """
    健康检查
    
    Returns:
        服务健康状态
    """
    try:
        # 调用ping服务测试
        ping_result = call_service("ping")
        
        return {
            "status": "healthy",
            "timestamp": datetime.now(),
            "service_status": ping_result.get("status", "unknown"),
            "running_tasks": len(_running_tasks)
        }
        
    except Exception as e:
        return {
            "status": "unhealthy",
            "timestamp": datetime.now(),
            "error": str(e),
            "running_tasks": len(_running_tasks)
        }

@router.delete("/task/{task_id}")
async def cancel_task(task_id: str):
    """
    取消异步任务
    
    Args:
        task_id: 任务ID
        
    Returns:
        取消结果
    """
    if task_id not in _running_tasks:
        raise HTTPException(
            status_code=404,
            detail=f"任务 {task_id} 不存在"
        )
    
    task_info = _running_tasks[task_id]
    
    if task_info["status"] in ["completed", "failed"]:
        raise HTTPException(
            status_code=400,
            detail=f"任务 {task_id} 已完成，无法取消"
        )
    
    # 标记任务为已取消
    _running_tasks[task_id]["status"] = "cancelled"
    
    return {
        "message": f"任务 {task_id} 已取消",
        "task_id": task_id
    }

async def _execute_async_service(task_id: str, service_name: str, 
                                params: Dict[str, Any], callback_url: Optional[str]):
    """
    执行异步服务
    
    Args:
        task_id: 任务ID
        service_name: 服务名称
        params: 服务参数
        callback_url: 回调URL
    """
    try:
        # 更新任务状态为运行中
        _running_tasks[task_id]["status"] = "running"
        
        # 执行服务
        result = call_service(service_name, **params)
        
        # 更新任务状态
        if result.get("status") == "success":
            _running_tasks[task_id]["status"] = "completed"
            _running_tasks[task_id]["result"] = result
        else:
            _running_tasks[task_id]["status"] = "failed"
            _running_tasks[task_id]["error"] = result.get("message", "未知错误")
        
        # 如果有回调URL，发送结果
        if callback_url:
            await _send_callback(callback_url, task_id, result)
            
    except Exception as e:
        # 更新任务状态为失败
        _running_tasks[task_id]["status"] = "failed"
        _running_tasks[task_id]["error"] = str(e)

async def _send_callback(callback_url: str, task_id: str, result: Dict[str, Any]):
    """
    发送回调通知
    
    Args:
        callback_url: 回调URL
        task_id: 任务ID
        result: 执行结果
    """
    try:
        import httpx
        
        async with httpx.AsyncClient() as client:
            await client.post(
                callback_url,
                json={
                    "task_id": task_id,
                    "result": result,
                    "timestamp": datetime.now().isoformat()
                },
                timeout=30
            )
    except Exception as e:
        print(f"回调发送失败: {e}")  # 在实际应用中应该使用日志
