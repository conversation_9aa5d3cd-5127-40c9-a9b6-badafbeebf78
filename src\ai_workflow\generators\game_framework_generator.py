"""
游戏框架生成模块 (Deepseek-R1 Phase I)
基于SAD生成结构化主线剧情节点和角色设定
"""

import os
import json
from typing import List, Dict
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section
from src.ai_workflow.utils.gpt_client import gpt
from src.ai_workflow.rag import RAGRetrieval
# 从提示词库加载
from prompts.framework_prompts import FRAMEWORK_GENERATION_PROMPTS

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

class GameFrameworkGenerator:
    def __init__(self):
        """初始化游戏框架生成器"""
        # 获取框架生成配置
        self.framework_config = get_section('generators', 'framework_generation')

        self.rag = RAGRetrieval()

        # 输出目录
        output_dir = self.framework_config.get('output_directory', 'frameworks')
        self.framework_dir = os.path.join(game_directory, "data", output_dir)
        os.makedirs(self.framework_dir, exist_ok=True)

        # 加载提示词
        self.prompts = FRAMEWORK_GENERATION_PROMPTS

    def generate_story_framework(self, theme: str = None, reference_materials: List[str] = None) -> Dict:
        """
        生成游戏主线剧情框架和角色设定
        使用Deepseek-R1 Phase I
        """
        print("开始生成游戏框架 (Phase I)...")
        
        # 1. 收集参考资料
        context = self.collect_reference_context(theme, reference_materials)
        
        # 2. 生成主线剧情骨架
        story_skeleton = self.generate_story_skeleton(theme, context)
        
        # 3. 生成角色设定
        characters = self.generate_character_settings(story_skeleton, context)
        
        # 4. 构建完整框架
        framework = {
            "theme": theme or "文化遗产探索",
            "story_skeleton": story_skeleton,
            "characters": characters,
            "reference_context": context,
            "generation_metadata": {
                "phase": "Phase I - Framework Generation",
                "model": "Deepseek-R1",
                "timestamp": self.get_current_time()
            }
        }
        
        # 5. 保存框架
        framework_path = self.save_framework(framework)
        
        print(f"游戏框架生成完成: {framework_path}")
        return framework

    def collect_reference_context(self, theme: str, reference_materials: List[str] = None) -> Dict:
        """收集参考上下文"""
        context = {
            "sad_summaries": [],
            "vsi_descriptions": [],
            "theme_related": []
        }
        
        # 如果有主题，检索相关内容
        if theme:
            sad_results = self.rag.search_sad(theme, top_k=5)
            context["sad_summaries"] = [
                {
                    "content": result["document"],
                    "source": result["metadata"].get("material_name", ""),
                    "similarity": result["similarity"]
                }
                for result in sad_results
            ]
            
            vsi_results = self.rag.search_vsi(theme, top_k=3)
            context["vsi_descriptions"] = [
                {
                    "description": result["document"],
                    "image_path": result["metadata"].get("image_path", ""),
                    "similarity": result["similarity"]
                }
                for result in vsi_results
            ]
        
        # 处理指定的参考材料
        if reference_materials:
            for material in reference_materials:
                material_results = self.rag.search(material, top_k=3)
                context["theme_related"].extend([
                    {
                        "content": result["document"],
                        "source": result["metadata"].get("material_name", ""),
                        "type": result["metadata"].get("type", ""),
                        "similarity": result["similarity"]
                    }
                    for result in material_results
                ])
        
        return context

    def generate_story_skeleton(self, theme: str, context: Dict) -> Dict:
        """生成主线剧情骨架"""
        
        # 构建参考资料文本
        reference_text = self.build_reference_text(context)
        
        system_prompt = FRAMEWORK_GENERATION_PROMPTS["story_skeleton"]["system"]
        user_prompt = FRAMEWORK_GENERATION_PROMPTS["story_skeleton"]["user"].format(
            theme=theme,
            reference_text=reference_text
        )

        try:
            response = gpt(system_prompt, user_prompt)
            return self.parse_story_skeleton(response)
        except Exception as e:
            print(f"剧情骨架生成失败: {e}")
            return self.generate_default_skeleton(theme)

    def parse_story_skeleton(self, response: str) -> Dict:
        """解析剧情骨架响应"""
        skeleton = {
            "mainline_nodes": [],
            "sideline_nodes": [],
            "node_relationships": []
        }
        
        lines = response.split('\n')
        current_node = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测节点编号
            if line.startswith(('A', 'B')) and ':' in line:
                if current_node:
                    # 保存前一个节点
                    if current_node["node_id"].startswith('A'):
                        skeleton["mainline_nodes"].append(current_node)
                    else:
                        skeleton["sideline_nodes"].append(current_node)
                
                # 开始新节点
                parts = line.split(':', 1)
                node_id = parts[0].strip()
                description = parts[1].strip() if len(parts) > 1 else ""
                
                current_node = {
                    "node_id": node_id,
                    "description": description,
                    "key_events": [],
                    "characters": [],
                    "references": []
                }
            elif current_node and line:
                # 添加到当前节点的详细信息
                if "关键事件" in line or "事件" in line:
                    current_node["key_events"].append(line)
                elif "角色" in line or "人物" in line:
                    current_node["characters"].append(line)
                elif "参考" in line or "引用" in line:
                    current_node["references"].append(line)
                else:
                    # 添加到描述中
                    current_node["description"] += " " + line
        
        # 保存最后一个节点
        if current_node:
            if current_node["node_id"].startswith('A'):
                skeleton["mainline_nodes"].append(current_node)
            else:
                skeleton["sideline_nodes"].append(current_node)
        
        return skeleton

    def generate_character_settings(self, story_skeleton: Dict, context: Dict) -> List[Dict]:
        """生成角色设定"""
        
        # 从剧情骨架中提取角色信息
        mentioned_characters = set()
        for node in story_skeleton.get("mainline_nodes", []):
            for char_info in node.get("characters", []):
                # 简单的角色名提取
                if ":" in char_info:
                    char_name = char_info.split(":")[0].strip()
                    mentioned_characters.add(char_name)
        
        reference_text = self.build_reference_text(context)
        
        system_prompt = FRAMEWORK_GENERATION_PROMPTS["character_settings"]["system"]
        user_prompt = FRAMEWORK_GENERATION_PROMPTS["character_settings"]["user"].format(
            mentioned_characters=', '.join(mentioned_characters),
            reference_text=reference_text
        )

        try:
            response = gpt(system_prompt, user_prompt)
            return self.parse_character_settings(response)
        except Exception as e:
            print(f"角色设定生成失败: {e}")
            return self.generate_default_characters()

    def parse_character_settings(self, response: str) -> List[Dict]:
        """解析角色设定响应"""
        characters = []
        lines = response.split('\n')
        current_character = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 检测角色名
            if ':' in line and not line.startswith(' '):
                if current_character:
                    characters.append(current_character)
                
                char_name = line.split(':')[0].strip()
                char_description = line.split(':', 1)[1].strip() if ':' in line else ""
                
                current_character = {
                    "name": char_name,
                    "description": char_description,
                    "details": []
                }
            elif current_character and line:
                current_character["details"].append(line)
        
        if current_character:
            characters.append(current_character)
        
        return characters

    def build_reference_text(self, context: Dict) -> str:
        """构建参考资料文本"""
        text_parts = []
        
        # SAD摘要
        if context.get("sad_summaries"):
            text_parts.append("文本摘要：")
            for summary in context["sad_summaries"][:3]:
                text_parts.append(f"- {summary['content'][:200]}...")
        
        # VSI描述
        if context.get("vsi_descriptions"):
            text_parts.append("\n图像描述：")
            for desc in context["vsi_descriptions"][:2]:
                text_parts.append(f"- {desc['description']}")
        
        # 主题相关
        if context.get("theme_related"):
            text_parts.append("\n相关资料：")
            for item in context["theme_related"][:3]:
                text_parts.append(f"- {item['content'][:150]}...")
        
        return '\n'.join(text_parts)

    def generate_default_skeleton(self, theme: str) -> Dict:
        """生成默认剧情骨架"""
        return {
            "mainline_nodes": [
                {
                    "node_id": "A1",
                    "description": f"主角开始探索{theme}的旅程",
                    "key_events": ["初次接触主题", "建立基本设定"],
                    "characters": ["主角"],
                    "references": []
                }
            ],
            "sideline_nodes": [],
            "node_relationships": []
        }

    def generate_default_characters(self) -> List[Dict]:
        """生成默认角色设定"""
        return [
            {
                "name": "主角",
                "description": "一位对文化遗产感兴趣的年轻人",
                "details": ["平凡的背景", "好奇心强", "容易代入"]
            }
        ]

    def save_framework(self, framework: Dict) -> str:
        """保存游戏框架"""
        timestamp = self.get_current_time().replace(':', '-').replace(' ', '_')
        filename = f"game_framework_{timestamp}.json"
        filepath = os.path.join(self.framework_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(framework, f, ensure_ascii=False, indent=2)
        
        # 同时保存为最新版本
        latest_path = os.path.join(self.framework_dir, "latest_framework.json")
        with open(latest_path, 'w', encoding='utf-8') as f:
            json.dump(framework, f, ensure_ascii=False, indent=2)
        
        return filepath

    def load_latest_framework(self) -> Dict:
        """加载最新的游戏框架"""
        latest_path = os.path.join(self.framework_dir, "latest_framework.json")
        if os.path.exists(latest_path):
            with open(latest_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
