# AI-GAL 后端服务架构文档

## 📋 项目概述

AI-GAL 后端服务是一个基于 **FastAPI** 的微服务架构，将原有的 CLI 命令行界面改造为 RESTful API 服务，为前端 GUI 提供统一的后端接口。

### 🎯 设计目标

- **服务化改造**：将 CLI 交互逻辑转换为可调用的 API 服务
- **统一接口**：提供一致的服务调用方式，避免复杂的 RESTful 设计
- **异步支持**：支持长时间运行的任务（如游戏生成）
- **易于扩展**：新功能只需添加服务注册即可
- **松耦合**：前端只需知道服务名和参数，无需了解具体实现

## 🏗️ 架构设计

### 整体架构图

```
┌─────────────────┐    HTTP API    ┌─────────────────┐
│   Frontend GUI  │ ──────────────► │  Backend API    │
│   (Electron/    │                │  (FastAPI)      │
│    PyQt/WPF)    │ ◄────────────── │                 │
└─────────────────┘    JSON        └─────────────────┘
                                            │
                                            ▼
                    ┌─────────────────────────────────────┐
                    │         Service Registry            │
                    │    (统一服务注册与调用机制)          │
                    └─────────────────────────────────────┘
                                            │
                    ┌───────────────────────┼───────────────────────┐
                    ▼                       ▼                       ▼
            ┌───────────────┐    ┌───────────────┐    ┌───────────────┐
            │ Game Services │    │  RAG Services │    │ Test Services │
            │   (游戏生成)   │    │   (检索服务)   │    │   (测试服务)   │
            └───────────────┘    └───────────────┘    └───────────────┘
                    │                       │                       │
                    ▼                       ▼                       ▼
            ┌───────────────────────────────────────────────────────────┐
            │              AI Workflow Core Modules                     │
            │  (controller, generators, preprocess, rag, utils)        │
            └───────────────────────────────────────────────────────────┘
```

### 核心组件说明

#### 1. **API 层 (server.py + router.py)**
- **职责**：处理 HTTP 请求，路由分发，异常处理
- **技术栈**：FastAPI + Uvicorn
- **特性**：
  - 自动生成 API 文档 (Swagger/ReDoc)
  - CORS 跨域支持
  - 全局异常处理
  - 请求/响应模型验证

#### 2. **服务注册层 (service_registry.py)**
- **职责**：服务注册、发现、调用管理
- **设计模式**：注册器模式 + 装饰器模式
- **核心功能**：
  - 服务自动注册 (`@register_service`)
  - 统一服务调用 (`call_service`)
  - 服务发现和元数据管理
  - 参数验证和类型检查

#### 3. **业务服务层 (game_services.py)**
- **职责**：封装具体的业务逻辑
- **设计原则**：将 CLI 的交互逻辑转换为纯函数
- **主要服务**：
  - 游戏生成服务
  - 素材预处理服务
  - RAG 检索服务
  - 系统测试服务
  - 配置检查服务

#### 4. **核心业务层 (src/ 目录)**
- **职责**：实际的 AI 工作流程实现
- **组件**：
  - `controller/`：主流程控制器
  - `generators/`：各种生成器（图像、语音、叙事等）
  - `preprocess/`：素材预处理
  - `rag/`：检索增强生成
  - `utils/`：工具函数

## 🔧 技术架构详解

### 1. 服务注册机制

```python
# 服务注册示例
@register_service(
    "generate_complete_game",
    "生成完整的文化遗产严肃游戏",
    {
        "theme": {"type": "str", "description": "游戏主题", "required": True},
        "material_paths": {"type": "List[str]", "description": "素材文件路径", "required": True}
    }
)
def generate_complete_game_service(theme: str, material_paths: List[str]):
    return _game_services.generate_complete_game(theme, material_paths)
```

**优势**：
- 自动服务发现
- 参数类型验证
- 统一错误处理
- 元数据管理

### 2. 统一调用接口

所有服务通过统一的 `/api/v1/call` 接口调用：

```json
POST /api/v1/call
{
  "service": "generate_complete_game",
  "params": {
    "theme": "雷州石狗文化",
    "material_paths": ["materials/雷州文化.txt"]
  }
}
```

**响应格式**：
```json
{
  "status": "success|error",
  "message": "响应消息",
  "data": "具体数据",
  "timestamp": "2024-01-01T12:00:00"
}
```

### 3. 异步任务支持

对于长时间运行的任务（如游戏生成），提供异步调用：

```json
POST /api/v1/call-async
{
  "service": "generate_complete_game",
  "params": {...},
  "callback_url": "http://frontend/callback"  // 可选
}
```

**任务管理**：
- 任务创建：返回 `task_id`
- 状态查询：`GET /api/v1/task/{task_id}`
- 任务取消：`DELETE /api/v1/task/{task_id}`

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装后端依赖
pip install -r backend/requirements.txt

# 安装项目依赖
pip install -r requirements.txt
```

### 2. 启动服务

```bash
# 方式一：使用启动脚本（推荐）
python start_backend.py

# 方式二：直接启动
python -m backend.server --host 127.0.0.1 --port 8080

# 方式三：开发模式（自动重载）
python -m backend.server --reload
```

### 3. 验证服务

```bash
# 运行测试脚本
python backend/test_backend.py

# 访问 API 文档
# http://127.0.0.1:8080/docs
```

## 📚 API 接口文档

启动服务后，可通过以下地址访问完整的 API 文档：

- **Swagger UI**: http://127.0.0.1:8080/docs
- **ReDoc**: http://127.0.0.1:8080/redoc

## 🔧 核心接口说明

### 基础接口

| 接口 | 方法 | 描述 | 示例 |
|------|------|------|------|
| `/` | GET | 根路径，返回API基本信息 | `curl http://127.0.0.1:8080/` |
| `/health` | GET | 健康检查 | `curl http://127.0.0.1:8080/health` |
| `/api/v1/` | GET | API根路径信息 | `curl http://127.0.0.1:8080/api/v1/` |

### 服务管理接口

| 接口 | 方法 | 描述 | 响应示例 |
|------|------|------|----------|
| `/api/v1/services` | GET | 获取所有可用服务列表 | `{"status": "success", "data": {"services": {...}}}` |
| `/api/v1/services/{name}` | GET | 获取特定服务详细信息 | `{"status": "success", "data": {"description": "..."}}` |

### 核心调用接口

#### 1. 同步服务调用

```http
POST /api/v1/call
Content-Type: application/json

{
  "service": "服务名称",
  "params": {
    "参数名": "参数值"
  }
}
```

**特点**：
- 立即返回结果
- 适用于快速执行的服务
- 超时时间：5分钟

#### 2. 异步服务调用

```http
POST /api/v1/call-async
Content-Type: application/json

{
  "service": "服务名称",
  "params": {
    "参数名": "参数值"
  },
  "callback_url": "http://your-app/callback"  // 可选
}
```

**特点**：
- 立即返回任务ID
- 适用于长时间运行的任务
- 支持回调通知

#### 3. 任务管理接口

| 接口 | 方法 | 描述 |
|------|------|------|
| `/api/v1/task/{task_id}` | GET | 查询异步任务状态 |
| `/api/v1/task/{task_id}` | DELETE | 取消异步任务 |

**任务状态**：
- `pending`：等待执行
- `running`：正在执行
- `completed`：执行完成
- `failed`：执行失败
- `cancelled`：已取消

## 📝 详细使用示例

### 1. 基础连通性测试

```bash
# 测试服务是否正常运行
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{"service": "ping", "params": {}}'

# 预期响应
{
  "status": "success",
  "message": "服务正常运行",
  "data": {
    "timestamp": 1704067200.0,
    "service_count": 9
  }
}
```

### 2. 获取系统信息

```bash
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{"service": "get_system_info", "params": {}}'

# 预期响应
{
  "status": "success",
  "message": "系统信息获取成功",
  "data": {
    "version": "2.0.0",
    "title": "AI-GAL 文化遗产游戏生成系统",
    "features": ["素材预处理", "RAG检索系统", ...]
  }
}
```

### 3. 检查配置状态

```bash
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{"service": "check_configuration", "params": {}}'

# 预期响应
{
  "status": "success",
  "data": {
    "config_status": [
      {"name": "GPT API密钥", "status": "已配置", "value": "sk-***"},
      {"name": "图像生成模式", "status": "已配置", "value": "False"}
    ],
    "dependency_status": [
      {"name": "文本处理 (spacy)", "status": "已安装"},
      {"name": "图像处理 (PIL)", "status": "已安装"}
    ]
  }
}
```

### 4. 生成完整游戏（同步调用）

```bash
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{
    "service": "generate_complete_game",
    "params": {
      "theme": "雷州石狗文化遗产",
      "material_paths": ["example_materials/雷州石狗文化.txt"],
      "reference_materials": ["石狗传说", "雷州历史", "民间信仰"]
    }
  }'

# 注意：游戏生成可能需要很长时间，建议使用异步调用
```

### 5. 生成完整游戏（异步调用 - 推荐）

```bash
# 步骤1：创建异步任务
curl -X POST "http://127.0.0.1:8080/api/v1/call-async" \
  -H "Content-Type: application/json" \
  -d '{
    "service": "generate_complete_game",
    "params": {
      "theme": "雷州石狗文化遗产",
      "material_paths": ["example_materials/雷州石狗文化.txt"],
      "reference_materials": ["石狗传说", "雷州历史"]
    }
  }'

# 响应示例
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "pending",
  "message": "任务已创建，正在执行中"
}

# 步骤2：查询任务状态
curl "http://127.0.0.1:8080/api/v1/task/550e8400-e29b-41d4-a716-446655440000"

# 状态响应示例
{
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "status": "running",  // pending -> running -> completed/failed
  "service": "generate_complete_game",
  "created_at": "2024-01-01T12:00:00",
  "result": null  // 完成后包含结果
}
```

### 6. 测试素材预处理

```bash
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{
    "service": "test_material_preprocessing",
    "params": {
      "test_text": "雷州石狗是广东雷州半岛特有的文化遗产，承载着深厚的历史文化内涵。"
    }
  }'
```

### 7. 测试RAG检索

```bash
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{
    "service": "test_rag_retrieval",
    "params": {
      "query": "石狗传说",
      "top_k": 5
    }
  }'
```

## 🎯 完整服务清单

### 核心业务服务

| 服务名称 | 描述 | 调用方式 | 参数说明 | 执行时间 |
|---------|------|---------|----------|----------|
| `generate_complete_game` | 生成完整的文化遗产严肃游戏 | 同步/异步 | `theme`(必需), `material_paths`(必需), `reference_materials`(可选) | 5-30分钟 |
| `test_material_preprocessing` | 测试素材预处理功能 | 同步 | `test_text`(可选) | 10-30秒 |
| `test_rag_retrieval` | 测试RAG检索功能 | 同步 | `query`(可选), `top_k`(可选) | 1-5秒 |
| `run_system_test` | 运行系统综合测试 | 同步 | 无 | 30-60秒 |

### 系统管理服务

| 服务名称 | 描述 | 调用方式 | 参数说明 | 执行时间 |
|---------|------|---------|----------|----------|
| `ping` | 测试服务连通性 | 同步 | 无 | <1秒 |
| `get_system_info` | 获取系统基本信息 | 同步 | 无 | <1秒 |
| `check_configuration` | 检查系统配置状态 | 同步 | 无 | 1-3秒 |

### 服务发现服务

| 服务名称 | 描述 | 调用方式 | 参数说明 | 执行时间 |
|---------|------|---------|----------|----------|
| `list_services` | 获取所有可用服务列表 | 同步 | 无 | <1秒 |
| `service_info` | 获取特定服务的详细信息 | 同步 | `service_name`(必需) | <1秒 |

### 参数详细说明

#### `generate_complete_game` 参数

```json
{
  "theme": "游戏主题，如：雷州石狗文化遗产",
  "material_paths": [
    "素材文件路径1.txt",
    "素材文件路径2.pdf",
    "图像文件路径.jpg"
  ],
  "reference_materials": [
    "参考关键词1",
    "参考关键词2"
  ]
}
```

#### `test_rag_retrieval` 参数

```json
{
  "query": "检索查询词，如：石狗传说",
  "top_k": 5  // 返回结果数量，默认3
}
```

#### `test_material_preprocessing` 参数

```json
{
  "test_text": "要处理的测试文本内容"
}
```

## 🔧 配置说明

### 配置文件位置
后端服务会自动读取项目根目录下的 `config.ini` 配置文件。

### 必需配置项

```ini
[CHATGPT]
gpt_key = your_deepseek_api_key          # DeepSeek API密钥
base_url = https://api.deepseek.com      # API地址
model = deepseek-reasoner                # 使用的模型

[AI绘画]
if_cloud = False                         # 是否使用云端图像生成
draw_key = your_draw_key                 # 图像生成API密钥（云端时需要）

[SOVITS]
if_cloud = False                         # 是否使用云端语音生成
语音key = your_voice_key                 # 语音生成API密钥（云端时需要）
version = 0                              # SOVITS版本 (0=V1, 1=V2)

[AI音乐]
if_on = True                            # 是否启用音乐生成
```

### 配置检查

启动服务后，可以通过以下方式检查配置：

```bash
# 方式1：API调用
curl -X POST "http://127.0.0.1:8080/api/v1/call" \
  -H "Content-Type: application/json" \
  -d '{"service": "check_configuration", "params": {}}'

# 方式2：测试脚本
python backend/test_backend.py
```

## 🏃‍♂️ 前端集成指南

### 1. 基础集成模式

```javascript
// JavaScript 示例
class AIGALClient {
  constructor(baseUrl = 'http://127.0.0.1:8080') {
    this.apiBase = `${baseUrl}/api/v1`;
  }

  async callService(serviceName, params = {}) {
    const response = await fetch(`${this.apiBase}/call`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ service: serviceName, params })
    });
    return await response.json();
  }

  async callServiceAsync(serviceName, params = {}) {
    const response = await fetch(`${this.apiBase}/call-async`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ service: serviceName, params })
    });
    const data = await response.json();
    return data.task_id;
  }

  async getTaskStatus(taskId) {
    const response = await fetch(`${this.apiBase}/task/${taskId}`);
    return await response.json();
  }
}

// 使用示例
const client = new AIGALClient();

// 同步调用
const result = await client.callService('ping');
console.log(result);

// 异步调用
const taskId = await client.callServiceAsync('generate_complete_game', {
  theme: '雷州石狗文化',
  material_paths: ['materials/文化资料.txt']
});

// 轮询任务状态
const checkTask = async () => {
  const status = await client.getTaskStatus(taskId);
  if (status.status === 'completed') {
    console.log('任务完成:', status.result);
  } else if (status.status === 'failed') {
    console.log('任务失败:', status.error);
  } else {
    setTimeout(checkTask, 2000); // 2秒后再次检查
  }
};
checkTask();
```

### 2. Python 客户端示例

```python
import requests
import time

class AIGALClient:
    def __init__(self, base_url="http://127.0.0.1:8080"):
        self.api_base = f"{base_url}/api/v1"

    def call_service(self, service_name, params=None):
        if params is None:
            params = {}

        response = requests.post(
            f"{self.api_base}/call",
            json={"service": service_name, "params": params},
            timeout=300
        )
        return response.json()

    def call_service_async(self, service_name, params=None):
        if params is None:
            params = {}

        response = requests.post(
            f"{self.api_base}/call-async",
            json={"service": service_name, "params": params}
        )
        return response.json().get("task_id")

    def get_task_status(self, task_id):
        response = requests.get(f"{self.api_base}/task/{task_id}")
        return response.json()

    def wait_for_task(self, task_id, timeout=300):
        start_time = time.time()
        while time.time() - start_time < timeout:
            status = self.get_task_status(task_id)
            if status.get("status") in ["completed", "failed", "cancelled"]:
                return status
            time.sleep(2)
        return {"status": "timeout"}

# 使用示例
client = AIGALClient()

# 生成游戏
task_id = client.call_service_async("generate_complete_game", {
    "theme": "雷州石狗文化遗产",
    "material_paths": ["materials/雷州文化.txt"]
})

print(f"任务已创建: {task_id}")
result = client.wait_for_task(task_id)
print(f"任务结果: {result}")
```

## 🐛 故障排除

### 常见问题及解决方案

#### 1. 服务启动失败

**问题现象**：
```
ModuleNotFoundError: No module named 'fastapi'
```

**解决方案**：
```bash
pip install -r backend/requirements.txt
```

#### 2. 端口被占用

**问题现象**：
```
OSError: [Errno 48] Address already in use
```

**解决方案**：
```bash
# 查找占用端口的进程
lsof -i :8080  # macOS/Linux
netstat -ano | findstr :8080  # Windows

# 更换端口启动
python -m backend.server --port 8081
```

#### 3. 配置文件未找到

**问题现象**：
```
⚠️ 配置文件未找到，请检查 config.ini
```

**解决方案**：
```bash
# 确保在项目根目录下有 config.ini 文件
ls config.ini

# 如果没有，从示例复制
cp config.ini.example config.ini
```

#### 4. API调用失败

**问题现象**：
```json
{
  "status": "error",
  "message": "服务调用失败: ..."
}
```

**解决方案**：
1. 检查服务名称是否正确
2. 检查参数格式是否正确
3. 查看服务器日志获取详细错误信息
4. 运行测试脚本诊断：`python backend/test_backend.py`

#### 5. 长时间任务超时

**问题现象**：
异步任务长时间处于 `running` 状态

**解决方案**：
1. 检查系统资源使用情况
2. 确认配置文件中的API密钥有效
3. 检查网络连接状态
4. 查看服务器日志获取详细信息

### 调试工具

#### 1. 健康检查
```bash
curl http://127.0.0.1:8080/health
```

#### 2. 服务测试
```bash
python backend/test_backend.py --url http://127.0.0.1:8080
```

#### 3. 查看日志
服务启动时会在控制台输出详细日志，包括：
- 请求处理日志
- 错误信息
- 性能统计

## 📞 技术支持

### 获取帮助的方式

1. **API文档**：http://127.0.0.1:8080/docs
2. **测试脚本**：`python backend/test_backend.py`
3. **示例代码**：`python backend/example_client.py`
4. **配置检查**：调用 `check_configuration` 服务

### 报告问题时请提供

1. 错误信息的完整日志
2. 使用的操作系统和Python版本
3. 配置文件内容（隐藏敏感信息）
4. 重现问题的具体步骤
