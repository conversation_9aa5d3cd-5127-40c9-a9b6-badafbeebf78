# AI-GAL 项目文档索引

## 📚 文档概览

本目录包含 AI-GAL 文化遗产游戏生成系统的完整文档，涵盖项目设计、架构说明、使用指南等内容。

## 📋 文档列表

### 🎯 项目核心文档

| 文档名称 | 描述 | 适用对象 |
|---------|------|----------|
| [大纲文档.md](./大纲文档.md) | 项目整体设计大纲和技术路线 | 项目经理、架构师 |
| [backend_architecture.md](./backend_architecture.md) | 后端架构详细设计文档 | 后端开发者 |
| [architecture_diagrams.md](./architecture_diagrams.md) | 系统架构图表和流程图 | 所有开发者 |

### 🔧 技术文档

| 文档名称 | 描述 | 适用对象 |
|---------|------|----------|
| [../backend/README.md](../backend/README.md) | 后端服务使用指南 | 前端开发者、测试人员 |
| [../src/generators/README.md](../src/generators/README.md) | 生成器模块说明 | 算法开发者 |
| [../src/rag/README.md](../src/ai_workflow/rag/README.md) | RAG检索系统文档 | AI工程师 |

### 📖 使用指南

| 文档名称 | 描述 | 适用对象 |
|---------|------|----------|
| [../README.md](../README.md) | 项目总体介绍和快速开始 | 所有用户 |
| [../backend/example_client.py](../backend/example_client.py) | 客户端调用示例代码 | 前端开发者 |
| [../start_backend.py](../start_backend.py) | 后端服务启动脚本 | 运维人员 |

## 🏗️ 项目架构概述

### 整体架构
AI-GAL 采用前后端分离的架构设计：

```
Frontend (GUI) ←→ Backend API ←→ Core AI Modules
```

### 后端架构
后端采用分层架构设计：

```
API Layer (FastAPI)
    ↓
Service Layer (Service Registry)
    ↓  
Business Layer (Game Services)
    ↓
Core Layer (AI Workflow)
```

## 🚀 快速导航

### 🎯 我想了解项目整体设计
👉 阅读 [大纲文档.md](./大纲文档.md)

### 🔧 我想了解后端架构
👉 阅读 [backend_architecture.md](./backend_architecture.md)
👉 查看 [architecture_diagrams.md](./architecture_diagrams.md)

### 💻 我想开发前端界面
👉 阅读 [../backend/README.md](../backend/README.md)
👉 参考 [../backend/example_client.py](../backend/example_client.py)

### 🧪 我想测试系统功能
👉 运行 `python start_backend.py`
👉 执行 `python backend/test_backend.py`

### 🔬 我想扩展AI功能
👉 查看 [../src/generators/](../src/generators/) 目录
👉 阅读 [../src/rag/README.md](../src/ai_workflow/rag/README.md)

## 📊 项目状态

### ✅ 已完成功能

1. **后端服务化改造**
   - CLI 转 API 服务
   - 统一服务注册机制
   - 同步/异步调用支持

2. **核心AI模块**
   - 素材预处理系统
   - RAG检索增强生成
   - 游戏框架生成器
   - 节点叙事生成器
   - 统一图像生成器
   - 统一语音生成器

3. **系统集成**
   - 主流程控制器
   - Ren'Py脚本生成
   - UI设计模块

### 🚧 开发中功能

1. **前端GUI界面**
   - 用户交互界面
   - 进度监控面板
   - 配置管理界面

2. **系统优化**
   - 性能优化
   - 错误处理完善
   - 日志系统

### 📋 待开发功能

1. **高级功能**
   - 批量游戏生成
   - 模板管理系统
   - 用户偏好学习

2. **部署支持**
   - Docker容器化
   - 云端部署支持
   - 分布式处理

## 🎯 开发指南

### 环境要求
- Python 3.8+
- Node.js 16+ (如果开发Web前端)
- GPU支持 (可选，用于本地AI生成)

### 开发流程
1. **环境搭建**：按照 README.md 安装依赖
2. **启动后端**：运行 `python start_backend.py`
3. **测试功能**：执行 `python backend/test_backend.py`
4. **开发前端**：参考 API 文档开发界面
5. **集成测试**：运行完整的端到端测试

### 代码规范
- 遵循 PEP 8 Python 代码规范
- 使用类型注解 (Type Hints)
- 编写单元测试
- 添加详细的文档字符串

## 🤝 贡献指南

### 如何贡献
1. Fork 项目仓库
2. 创建功能分支
3. 提交代码更改
4. 编写测试用例
5. 提交 Pull Request

### 报告问题
- 使用 GitHub Issues 报告 Bug
- 提供详细的重现步骤
- 包含系统环境信息
- 附上相关日志文件

## 📞 技术支持

### 获取帮助
- 📖 查看项目文档
- 🧪 运行测试脚本诊断问题
- 💬 在 GitHub Issues 中提问
- 📧 联系项目维护者

### 常见问题
1. **服务启动失败** → 检查依赖安装和端口占用
2. **API调用失败** → 验证请求格式和参数
3. **生成结果异常** → 检查配置文件和API密钥
4. **性能问题** → 查看系统资源使用情况

## 📈 项目路线图

### 短期目标 (1-2个月)
- [ ] 完善前端GUI界面
- [ ] 优化生成质量和速度
- [ ] 增加更多文化主题支持

### 中期目标 (3-6个月)
- [ ] 支持多语言生成
- [ ] 添加游戏模板系统
- [ ] 实现云端部署

### 长期目标 (6个月以上)
- [ ] 支持VR/AR游戏生成
- [ ] 集成更多AI模型
- [ ] 建立用户社区平台

---

📝 **文档更新时间**: 2024年1月
🔄 **文档版本**: v1.0.0
👥 **维护团队**: AI-GAL Development Team
