"""
检索器类
负责向量检索逻辑，支持向量存储和相似度查询
"""

import logging
import numpy as np
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple, Union
import pickle

try:
    import faiss
except ImportError:
    faiss = None

from sklearn.metrics.pairwise import cosine_similarity


class Retriever:
    """
    检索器类，负责处理向量检索逻辑，可连接向量数据库或使用近邻搜索算法
    """
    
    def __init__(self, index_dir: str = "embeddings", 
                 use_faiss: bool = True,
                 dimension: int = 1024):
        """
        初始化检索器
        
        Args:
            index_dir: 索引文件保存目录
            use_faiss: 是否使用FAISS索引
            dimension: 向量维度
        """
        self.index_dir = Path(index_dir)
        self.index_dir.mkdir(exist_ok=True)
        
        self.use_faiss = use_faiss and (faiss is not None)
        self.dimension = dimension
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化索引
        self.index = None
        self.metadata = []
        self.vectors = []
        
        if self.use_faiss:
            self._init_faiss_index()
        else:
            if faiss is None:
                self.logger.warning("FAISS未安装，将使用基础相似度计算")
            self.logger.info("使用基础向量检索")
    
    def _init_faiss_index(self):
        """初始化FAISS索引"""
        try:
            # 使用L2距离的平面索引
            self.index = faiss.IndexFlatL2(self.dimension)
            self.logger.info(f"FAISS索引初始化完成，维度: {self.dimension}")
        except Exception as e:
            self.logger.error(f"FAISS索引初始化失败: {str(e)}")
            self.use_faiss = False

    def add_vectors(self, vectors: Union[np.ndarray, List[np.ndarray]],
                    metadata: List[Dict[str, Any]]) -> None:
        """
        添加向量到索引

        Args:
            vectors: 向量数组或向量列表
            metadata: 对应的元数据列表
        """
        # 统一输入类型：转换为NumPy数组
        if isinstance(vectors, list):
            if not vectors:
                self.logger.warning("尝试添加空向量列表")
                return

            # 确保所有元素都是NumPy数组
            if not all(isinstance(v, np.ndarray) for v in vectors):
                try:
                    vectors = [np.array(v) for v in vectors]
                except Exception as e:
                    self.logger.error(f"无法将列表元素转换为NumPy数组: {str(e)}")
                    return

            # 堆叠为二维数组
            try:
                vectors = np.stack(vectors)
            except Exception as e:
                self.logger.error(f"堆叠向量失败: {str(e)}")
                return

        # 验证数据一致性
        if len(vectors) != len(metadata):
            self.logger.error(f"向量数量({len(vectors)})与元数据数量({len(metadata)})不匹配")
            return

        # 确保向量是正确的形状
        if vectors.ndim == 1:
            vectors = vectors.reshape(1, -1)
        elif vectors.ndim != 2:
            self.logger.error(f"不支持的向量维度: {vectors.ndim}，应为1维或2维")
            return

        # 更新维度（如果需要）
        if self.dimension != vectors.shape[1]:
            self.logger.info(f"更新索引维度: {self.dimension} -> {vectors.shape[1]}")
            self.dimension = vectors.shape[1]
            if self.use_faiss:
                self._init_faiss_index()

        try:
            if self.use_faiss:
                # 添加到FAISS索引
                self.index.add(vectors.astype(np.float32))
            else:
                # 添加到内存列表
                if len(self.vectors) == 0:
                    self.vectors = vectors
                else:
                    self.vectors = np.vstack([self.vectors, vectors])

            # 添加元数据
            self.metadata.extend(metadata)

            self.logger.info(f"成功添加了{len(vectors)}个向量到索引")

        except Exception as e:
            self.logger.error(f"添加向量失败: {str(e)}")
            # 不再重新抛出异常，避免中断主流程
    
    def query(self, query_vector: np.ndarray, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        查询相似向量
        
        Args:
            query_vector: 查询向量
            top_k: 返回最相似的k个结果
            
        Returns:
            相似结果列表，包含相似度和元数据
        """
        if len(self.metadata) == 0:
            self.logger.warning("索引为空，无法查询")
            return []
        
        # 确保查询向量是正确的形状
        if query_vector.ndim == 1:
            query_vector = query_vector.reshape(1, -1)
        
        try:
            if self.use_faiss:
                return self._query_faiss(query_vector, top_k)
            else:
                return self._query_basic(query_vector, top_k)
                
        except Exception as e:
            self.logger.error(f"查询失败: {str(e)}")
            raise
    
    def _query_faiss(self, query_vector: np.ndarray, top_k: int) -> List[Dict[str, Any]]:
        """
        使用FAISS进行查询
        
        Args:
            query_vector: 查询向量
            top_k: 返回数量
            
        Returns:
            查询结果列表
        """
        # FAISS搜索
        distances, indices = self.index.search(query_vector.astype(np.float32), top_k)
        
        results = []
        for i, (distance, idx) in enumerate(zip(distances[0], indices[0])):
            if idx < len(self.metadata):
                result = {
                    'rank': i + 1,
                    'similarity': 1 / (1 + distance),  # 转换为相似度
                    'distance': float(distance),
                    'metadata': self.metadata[idx]
                }
                results.append(result)
        
        return results
    
    def _query_basic(self, query_vector: np.ndarray, top_k: int) -> List[Dict[str, Any]]:
        """
        使用基础方法进行查询
        
        Args:
            query_vector: 查询向量
            top_k: 返回数量
            
        Returns:
            查询结果列表
        """
        # 计算余弦相似度
        similarities = cosine_similarity(query_vector, self.vectors)[0]
        
        # 获取top_k个最相似的索引
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for i, idx in enumerate(top_indices):
            result = {
                'rank': i + 1,
                'similarity': float(similarities[idx]),
                'distance': 1 - float(similarities[idx]),
                'metadata': self.metadata[idx]
            }
            results.append(result)
        
        return results
    
    def save_index(self, filename: str) -> str:
        """
        保存索引到文件
        
        Args:
            filename: 文件名
            
        Returns:
            保存的文件路径
        """
        try:
            if self.use_faiss:
                # 保存FAISS索引
                index_file = self.index_dir / f"{filename}.faiss"
                faiss.write_index(self.index, str(index_file))
            else:
                # 保存向量数组
                vectors_file = self.index_dir / f"{filename}_vectors.npy"
                np.save(vectors_file, self.vectors)
            
            # 保存元数据
            metadata_file = self.index_dir / f"{filename}_metadata.json"
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, ensure_ascii=False, indent=2)
            
            # 保存配置
            config = {
                'use_faiss': self.use_faiss,
                'dimension': self.dimension,
                'vector_count': len(self.metadata)
            }
            config_file = self.index_dir / f"{filename}_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"索引保存完成: {filename}")
            return str(self.index_dir / filename)
            
        except Exception as e:
            self.logger.error(f"索引保存失败: {str(e)}")
            raise
    
    def load_index(self, filename: str) -> None:
        """
        从文件加载索引
        
        Args:
            filename: 文件名
        """
        try:
            # 加载配置
            config_file = self.index_dir / f"{filename}_config.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.use_faiss = config.get('use_faiss', self.use_faiss)
                self.dimension = config.get('dimension', self.dimension)
            
            # 加载索引
            if self.use_faiss:
                index_file = self.index_dir / f"{filename}.faiss"
                if index_file.exists():
                    self.index = faiss.read_index(str(index_file))
                else:
                    raise FileNotFoundError(f"FAISS索引文件不存在: {index_file}")
            else:
                vectors_file = self.index_dir / f"{filename}_vectors.npy"
                if vectors_file.exists():
                    self.vectors = np.load(vectors_file)
                else:
                    raise FileNotFoundError(f"向量文件不存在: {vectors_file}")
            
            # 加载元数据
            metadata_file = self.index_dir / f"{filename}_metadata.json"
            if metadata_file.exists():
                with open(metadata_file, 'r', encoding='utf-8') as f:
                    self.metadata = json.load(f)
            else:
                self.logger.warning(f"元数据文件不存在: {metadata_file}")
                self.metadata = []
            
            self.logger.info(f"索引加载完成: {filename}, 向量数量: {len(self.metadata)}")
            
        except Exception as e:
            self.logger.error(f"索引加载失败: {str(e)}")
            raise
    
    def search_text(self, query_text: str, embedding_tool, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        文本查询接口
        
        Args:
            query_text: 查询文本
            embedding_tool: 向量化工具实例
            top_k: 返回数量
            
        Returns:
            查询结果列表
        """
        try:
            # 生成查询向量
            query_vector = embedding_tool.embed(query_text)
            
            # 执行查询
            results = self.query(query_vector, top_k)
            
            self.logger.info(f"文本查询完成: '{query_text[:50]}...', 返回{len(results)}个结果")
            return results
            
        except Exception as e:
            self.logger.error(f"文本查询失败: {str(e)}")
            raise
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取索引统计信息
        
        Returns:
            统计信息字典
        """
        stats = {
            'vector_count': len(self.metadata),
            'dimension': self.dimension,
            'use_faiss': self.use_faiss,
            'index_type': 'FAISS' if self.use_faiss else 'Basic'
        }
        
        if self.use_faiss and self.index:
            stats['faiss_total'] = self.index.ntotal
        
        return stats


if __name__ == "__main__":
    # 测试代码
    retriever = Retriever()
    
    # 示例用法
    print("Retriever类已创建")
    print("使用方法:")
    print("1. 添加向量: retriever.add_vectors(vectors, metadata)")
    print("2. 查询: retriever.query(query_vector, top_k)")
    print("3. 文本查询: retriever.search_text(query_text, embedding_tool)")
    print("4. 保存索引: retriever.save_index(filename)")
    print("5. 加载索引: retriever.load_index(filename)")
