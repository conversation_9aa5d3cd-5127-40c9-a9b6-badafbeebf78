"""
后端服务测试脚本
"""

import requests
import json
import time
from typing import Dict, Any

class BackendTester:
    """后端服务测试器"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        """
        初始化测试器
        
        Args:
            base_url: 后端服务基础URL
        """
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
    
    def test_health_check(self) -> bool:
        """测试健康检查"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            if response.status_code == 200:
                print("✅ 健康检查通过")
                return True
            else:
                print(f"❌ 健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_root_endpoint(self) -> bool:
        """测试根路径"""
        try:
            response = requests.get(f"{self.api_base}/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print("✅ 根路径访问成功")
                print(f"   API名称: {data.get('name')}")
                print(f"   版本: {data.get('version')}")
                return True
            else:
                print(f"❌ 根路径访问失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 根路径访问异常: {e}")
            return False
    
    def test_list_services(self) -> bool:
        """测试服务列表"""
        try:
            response = requests.get(f"{self.api_base}/services", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    services = data.get("data", {}).get("services", {})
                    print("✅ 服务列表获取成功")
                    print(f"   可用服务数量: {len(services)}")
                    for service_name in list(services.keys())[:3]:  # 显示前3个服务
                        print(f"   - {service_name}: {services[service_name].get('description', '')}")
                    return True
                else:
                    print(f"❌ 服务列表获取失败: {data.get('message')}")
                    return False
            else:
                print(f"❌ 服务列表请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 服务列表请求异常: {e}")
            return False
    
    def test_ping_service(self) -> bool:
        """测试ping服务"""
        try:
            payload = {
                "service": "ping",
                "params": {}
            }
            
            response = requests.post(
                f"{self.api_base}/call",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    print("✅ Ping服务调用成功")
                    print(f"   响应消息: {data.get('message')}")
                    return True
                else:
                    print(f"❌ Ping服务调用失败: {data.get('message')}")
                    return False
            else:
                print(f"❌ Ping服务请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Ping服务请求异常: {e}")
            return False
    
    def test_system_info_service(self) -> bool:
        """测试系统信息服务"""
        try:
            payload = {
                "service": "get_system_info",
                "params": {}
            }
            
            response = requests.post(
                f"{self.api_base}/call",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    system_data = data.get("data", {})
                    print("✅ 系统信息服务调用成功")
                    print(f"   系统标题: {system_data.get('title')}")
                    print(f"   系统版本: {system_data.get('version')}")
                    print(f"   功能数量: {len(system_data.get('features', []))}")
                    return True
                else:
                    print(f"❌ 系统信息服务调用失败: {data.get('message')}")
                    return False
            else:
                print(f"❌ 系统信息服务请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 系统信息服务请求异常: {e}")
            return False
    
    def test_config_check_service(self) -> bool:
        """测试配置检查服务"""
        try:
            payload = {
                "service": "check_configuration",
                "params": {}
            }
            
            response = requests.post(
                f"{self.api_base}/call",
                json=payload,
                timeout=15
            )
            
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "success":
                    config_data = data.get("data", {})
                    print("✅ 配置检查服务调用成功")
                    
                    config_status = config_data.get("config_status", [])
                    configured_count = sum(1 for item in config_status if item.get("status") == "已配置")
                    print(f"   配置项状态: {configured_count}/{len(config_status)} 已配置")
                    
                    dependency_status = config_data.get("dependency_status", [])
                    installed_count = sum(1 for item in dependency_status if item.get("status") == "已安装")
                    print(f"   依赖状态: {installed_count}/{len(dependency_status)} 已安装")
                    
                    return True
                else:
                    print(f"❌ 配置检查服务调用失败: {data.get('message')}")
                    return False
            else:
                print(f"❌ 配置检查服务请求失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 配置检查服务请求异常: {e}")
            return False
    
    def test_async_service(self) -> bool:
        """测试异步服务调用"""
        try:
            # 发起异步任务
            payload = {
                "service": "ping",
                "params": {}
            }
            
            response = requests.post(
                f"{self.api_base}/call-async",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                task_id = data.get("task_id")
                
                if task_id:
                    print("✅ 异步任务创建成功")
                    print(f"   任务ID: {task_id}")
                    
                    # 等待一段时间后查询任务状态
                    time.sleep(2)
                    
                    status_response = requests.get(
                        f"{self.api_base}/task/{task_id}",
                        timeout=5
                    )
                    
                    if status_response.status_code == 200:
                        status_data = status_response.json()
                        print(f"   任务状态: {status_data.get('status')}")
                        return True
                    else:
                        print(f"❌ 任务状态查询失败: {status_response.status_code}")
                        return False
                else:
                    print("❌ 异步任务创建失败: 未返回任务ID")
                    return False
            else:
                print(f"❌ 异步任务创建失败: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ 异步服务测试异常: {e}")
            return False
    
    def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🧪 开始后端服务测试")
        print("=" * 50)
        
        tests = [
            ("健康检查", self.test_health_check),
            ("根路径访问", self.test_root_endpoint),
            ("服务列表", self.test_list_services),
            ("Ping服务", self.test_ping_service),
            ("系统信息服务", self.test_system_info_service),
            ("配置检查服务", self.test_config_check_service),
            ("异步服务", self.test_async_service)
        ]
        
        results = {}
        passed = 0
        
        for test_name, test_func in tests:
            print(f"\n🔍 测试: {test_name}")
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed += 1
            except Exception as e:
                print(f"❌ 测试异常: {e}")
                results[test_name] = False
        
        print("\n" + "=" * 50)
        print("📊 测试结果汇总")
        print("=" * 50)
        
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n总体结果: {passed}/{len(tests)} 测试通过")
        
        if passed == len(tests):
            print("🎉 所有测试通过！后端服务运行正常")
        else:
            print("⚠️  部分测试失败，请检查服务配置")
        
        return results

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="AI-GAL 后端服务测试")
    parser.add_argument("--url", default="http://127.0.0.1:8080", help="后端服务URL")
    
    args = parser.parse_args()
    
    tester = BackendTester(args.url)
    results = tester.run_all_tests()
    
    # 返回退出码
    all_passed = all(results.values())
    exit(0 if all_passed else 1)

if __name__ == "__main__":
    main()
