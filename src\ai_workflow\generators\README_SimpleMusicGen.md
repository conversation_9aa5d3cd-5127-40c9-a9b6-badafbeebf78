# 简化版MusicGen音乐生成器

## 概述

这是一个极大简化的音乐生成器，仅支持本地MusicGen模型推理，去除了所有复杂的配置和云端功能。

## 核心特性

- **仅本地推理**: 只使用Meta的MusicGen模型进行本地音乐生成
- **简单配置**: 无需复杂配置，开箱即用
- **自动检测**: 自动检测依赖和GPU可用性
- **流程对接**: 与main_pipeline_controller完美对接

## 系统要求

### 最低要求
- Python 3.8+
- 4GB RAM (CPU模式)
- 2GB 存储空间

### 推荐配置
- Python 3.9+
- 8GB+ RAM
- NVIDIA GPU (4GB+ VRAM)
- 5GB+ 存储空间

## 快速安装

### 1. 安装依赖
```bash
pip install torch transformers scipy numpy
```

### 2. GPU支持 (可选)
```bash
# CUDA 11.8
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 使用方法

### 基本使用

```python
from music_generator import MusicGenerator

# 创建生成器
generator = MusicGenerator()

# 生成音乐
result = generator.generate_music(
    prompt="peaceful background music",
    filename="bgm_001",
    style="common"
)

print(f"生成结果: {result}")  # "ok" 或 "error"
```

### 支持的风格

- `common`: 平和的背景音乐
- `sad`: 悲伤忧郁的音乐
- `epic`: 史诗宏大的音乐
- `ambient`: 环境氛围音乐
- `upbeat`: 欢快活泼的音乐

### 与流程控制器对接

```python
# 在main_pipeline_controller中自动调用
narrative_data = {
    "node_id": "node_001",
    "narrative_content": {
        "music_instructions": [
            "peaceful piano music for introduction scene"
        ]
    }
}

result = generator.generate_music_for_node(narrative_data)
```

## 文件结构

```
music_generator.py              # 主要生成器类
test_simple_musicgen.py        # 测试脚本
install_musicgen_deps.py       # 依赖安装脚本 (如果存在)
README_SimpleMusicGen.md       # 本文档
```

## 输出

- **格式**: WAV (16位)
- **长度**: 约30秒
- **位置**: `{game_directory}/music/`
- **命名**: 自动添加`.wav`扩展名

## 默认配置

- **模型**: facebook/musicgen-small
- **设备**: 自动检测 (CUDA > CPU)
- **精度**: float16 (GPU) / float32 (CPU)
- **生成长度**: 1024 tokens (约30秒)

## 故障排除

### 常见问题

1. **ImportError: No module named 'torch'**
   ```bash
   pip install torch transformers scipy
   ```

2. **CUDA out of memory**
   - 使用CPU模式
   - 关闭其他程序
   - 使用更小的模型

3. **模型下载失败**
   - 检查网络连接
   - 使用代理
   - 手动下载模型

### 测试命令

```bash
# 运行测试
python test_simple_musicgen.py

# 直接测试
python music_generator.py
```

## 性能参考

| 设备 | 模型 | 生成时间 | 内存占用 |
|------|------|----------|----------|
| CPU (8核) | musicgen-small | ~2分钟 | ~2GB |
| RTX 3060 | musicgen-small | ~30秒 | ~3GB |
| RTX 4090 | musicgen-small | ~15秒 | ~4GB |

## 与原版对比

| 功能 | 原版 | 简化版 |
|------|------|--------|
| 云端生成 | ✓ | ✗ |
| 本地生成 | ✓ | ✓ |
| 复杂配置 | ✓ | ✗ |
| 多种模型 | ✓ | 固定small |
| 状态检查 | ✓ | 简化 |
| 错误处理 | 复杂 | 简单 |
| 代码行数 | 600+ | 250+ |

## 扩展说明

如需更多功能，可以：

1. **更换模型**: 修改`self.model_name`
2. **调整参数**: 修改`max_new_tokens`等
3. **添加风格**: 扩展`style_prompts`字典
4. **自定义输出**: 修改文件保存逻辑

## 许可证

遵循Meta MusicGen模型的许可证条款。
