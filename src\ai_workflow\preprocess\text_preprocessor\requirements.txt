# 文本预处理模块依赖包

# 核心依赖
numpy>=1.21.0
pandas>=1.3.0
scikit-learn>=1.0.0
openai>=1.0.0

# 文件解析依赖
pdfplumber>=0.7.0          # PDF文件解析
python-docx>=0.8.11        # DOCX文件解析

# 文本处理依赖
nltk>=3.9                  # 自然语言处理工具包
jieba>=0.42.1              # 中文分词
langchain>=0.2.0           # 文本分割工具

# 向量化依赖
sentence-transformers>=2.7.0  # 本地嵌入模型
transformers>=4.51.0          # Transformer模型
accelerate>=0.20.1
torch>=2.6.0                  # PyTorch

# 向量检索依赖
faiss-cpu>=1.7.0           # FAISS向量检索（CPU版本）
# faiss-gpu>=1.7.0          # FAISS向量检索（GPU版本，如需要）

# API调用依赖
openai>=1.0.0              # OpenAI API
requests>=2.28.0           # HTTP请求
httpx>=0.24.0              # 异步HTTP客户端

# 数据存储依赖
h5py>=3.7.0                # HDF5文件格式

# 日志和配置
loguru>=0.6.0              # 更好的日志工具
pydantic>=1.10.0           # 数据验证
python-dotenv>=0.19.0      # 环境变量管理

# 开发和测试依赖
pytest>=7.0.0              # 测试框架
pytest-cov>=4.0.0          # 测试覆盖率
black>=22.0.0              # 代码格式化
flake8>=5.0.0              # 代码检查
mypy>=0.991                # 类型检查

# 可选依赖（根据需要安装）
# jupyter>=1.0.0            # Jupyter Notebook
# matplotlib>=3.5.0         # 数据可视化
# seaborn>=0.11.0           # 统计可视化
# plotly>=5.0.0             # 交互式可视化

# 特定模型依赖（根据使用的模型选择）
# 通义千问嵌入模型相关
# dashscope>=1.0.0          # 阿里云模型服务SDK

# 其他可能用到的库
tqdm>=4.64.0               # 进度条

# 图像生成模块
diffusers>=0.34