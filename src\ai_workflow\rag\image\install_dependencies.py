"""
安装增强图像生成器所需的依赖
"""

import subprocess
import sys
import os


def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ Successfully installed {package}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install {package}: {e}")
        return False


def check_package(package_name, import_name=None):
    """检查包是否已安装"""
    if import_name is None:
        import_name = package_name
    
    try:
        __import__(import_name)
        print(f"✓ {package_name} is already installed")
        return True
    except ImportError:
        print(f"✗ {package_name} is not installed")
        return False


def main():
    """主安装函数"""
    print("Installing dependencies for Enhanced Image Generator")
    print("=" * 60)
    
    # 必需的包列表
    required_packages = [
        ("torch", "torch"),
        ("torchvision", "torchvision"),
        ("transformers", "transformers"),
        ("diffusers", "diffusers"),
        ("accelerate", "accelerate"),
        ("faiss-cpu", "faiss"),
        ("Pillow", "PIL"),
        ("numpy", "numpy"),
        ("safetensors", "safetensors")
    ]
    
    print("Checking existing packages...")
    print("-" * 40)
    
    packages_to_install = []
    for package_name, import_name in required_packages:
        if not check_package(package_name, import_name):
            packages_to_install.append(package_name)
    
    if not packages_to_install:
        print("\n🎉 All required packages are already installed!")
        return True
    
    print(f"\nInstalling {len(packages_to_install)} missing packages...")
    print("-" * 40)
    
    success_count = 0
    for package in packages_to_install:
        if install_package(package):
            success_count += 1
    
    print("\n" + "=" * 60)
    print("Installation Summary")
    print("=" * 60)
    print(f"Successfully installed: {success_count}/{len(packages_to_install)} packages")
    
    if success_count == len(packages_to_install):
        print("🎉 All packages installed successfully!")
        
        print("\nNext steps:")
        print("1. Ensure CLIP model is available at: src/ai_workflow/models/clip-vit-base-patch16")
        print("2. Ensure Stable Diffusion model is available at: src/ai_workflow/models/anything-v5")
        print("3. Add some test images to: src/ai_workflow/input/image")
        print("4. Run the test script: python test_enhanced_generator.py")
        
        return True
    else:
        print("⚠️  Some packages failed to install. Please check the error messages above.")
        return False


if __name__ == "__main__":
    main()
