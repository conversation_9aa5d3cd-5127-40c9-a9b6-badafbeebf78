# MusicGen 音乐生成器使用指南

## 概述

MusicGen音乐生成器使用Meta的MusicGen模型进行本地音乐生成，支持根据文本提示生成高质量的背景音乐。

## 功能特性

- **本地生成**: 使用MusicGen模型在本地生成音乐，无需网络连接
- **多种风格**: 支持sad、common、epic、ambient、upbeat等多种音乐风格
- **GPU加速**: 自动检测并使用GPU加速（如果可用）
- **配置灵活**: 支持新的JSON配置和旧的INI配置
- **智能检测**: 自动检测模型和依赖的可用性

## 系统要求

### 硬件要求
- **内存**: 至少8GB RAM
- **存储**: 至少5GB可用空间（用于模型文件）
- **GPU**: NVIDIA GPU（可选，用于加速）

### 软件依赖
- Python 3.8+
- PyTorch
- Transformers
- SciPy
- NumPy
- SoundFile

## 安装步骤

### 1. 自动安装（推荐）

运行自动安装脚本：
```bash
python install_musicgen_deps.py
```

### 2. 手动安装

#### 安装PyTorch（GPU版本）
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

#### 安装PyTorch（CPU版本）
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
```

#### 安装其他依赖
```bash
pip install transformers scipy numpy soundfile librosa
```

## 配置说明

### JSON配置（推荐）

在 `src/config/config_generators.json` 中配置：

```json
{
  "music_generation": {
    "enabled": true,
    "use_cloud": false,
    "local_model_path": "facebook/musicgen-medium",
    "output_directory": "music",
    "default_style": "common",
    "audio_format": "wav",
    "timeout": 600,
    "styles": {
      "sad": "Pure music, light music, game, galgame, piano, sad, violin, melancholy",
      "common": "Pure music, light music, relaxed, cafe, game, galgame, piano, peaceful",
      "epic": "Epic, orchestral, cinematic, dramatic, game, adventure, heroic",
      "ambient": "Ambient, atmospheric, peaceful, meditation, background, soft",
      "upbeat": "Upbeat, energetic, positive, cheerful, game, electronic, happy"
    }
  }
}
```

### INI配置（兼容）

在 `config.ini` 中配置：

```ini
[AI音乐]
if_on = true
if_cloud = false
local_model_path = facebook/musicgen-medium
local_output_dir = music
```

## 模型选择

### 可用模型
- `facebook/musicgen-small`: 300M参数，速度快，质量中等
- `facebook/musicgen-medium`: 1.5B参数，平衡速度和质量
- `facebook/musicgen-large`: 3.3B参数，质量最高，速度较慢

### 推荐配置
- **开发测试**: musicgen-small
- **生产环境**: musicgen-medium
- **高质量需求**: musicgen-large

## 使用方法

### 基本使用

```python
from music_generator import MusicGenerator

# 创建生成器
generator = MusicGenerator()

# 生成音乐
result = generator.generate_music(
    prompt="peaceful background music for cultural heritage game",
    filename="bgm_peaceful",
    style="common"
)

print(f"生成结果: {result}")
```

### 批量生成

```python
# 为游戏生成一套背景音乐
results = generator.generate_background_music_set(
    theme="cultural_heritage",
    scene_count=5
)

for result in results:
    print(f"场景: {result['scene_name']}, 状态: {result['status']}")
```

### 节点音乐生成

```python
# 为特定节点生成音乐
narrative_data = {
    "node_id": "node_001",
    "narrative_content": {
        "music_instructions": [
            "peaceful piano music for introduction scene",
            "dramatic orchestral music for climax"
        ]
    }
}

result = generator.generate_music_for_node(narrative_data)
print(f"节点音乐生成结果: {result}")
```

## 测试和验证

### 运行测试脚本

```bash
python test_musicgen.py
```

测试脚本会：
1. 检查依赖是否正确安装
2. 测试模型加载和推理
3. 生成测试音频文件
4. 验证MusicGenerator类功能

### 检查生成器状态

```python
generator = MusicGenerator()
status = generator.get_generation_status()
print(status)
```

## 故障排除

### 常见问题

1. **内存不足**
   - 使用更小的模型（musicgen-small）
   - 减少max_new_tokens参数
   - 关闭其他占用内存的程序

2. **CUDA错误**
   - 检查CUDA版本兼容性
   - 更新GPU驱动
   - 使用CPU模式作为备选

3. **模型下载失败**
   - 检查网络连接
   - 使用代理或镜像源
   - 手动下载模型文件

4. **音频质量问题**
   - 调整guidance_scale参数
   - 优化提示词描述
   - 尝试不同的模型

### 性能优化

1. **GPU优化**
   - 使用float16精度
   - 启用device_map="auto"
   - 定期清理GPU缓存

2. **内存优化**
   - 及时删除不用的模型
   - 使用torch.cuda.empty_cache()
   - 分批处理大量音乐生成

## 更新日志

### v1.0.0
- 集成MusicGen模型
- 支持本地音乐生成
- 添加多种音乐风格
- 实现配置系统兼容
- 添加自动依赖检测

## 支持和反馈

如果遇到问题或有改进建议，请：
1. 检查本文档的故障排除部分
2. 运行测试脚本诊断问题
3. 提交详细的错误报告

## 许可证

本项目使用的MusicGen模型遵循Meta的许可证条款。请确保在使用前了解相关限制。
