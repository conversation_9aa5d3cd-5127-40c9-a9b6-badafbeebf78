"""
文本预处理模块主流程
整合所有工具类，实现完整的文本预处理流程，从文件解析到向量化存储
"""

import json
import logging
import time
from pathlib import Path
from typing import Dict, Any, List, Optional

# 导入工具类
from tools.parser import Parser
from tools.splitter import Splitter
from tools.summarizer import Summarizer


class TextProcessor:
    """
    纯文本处理器，专注于文本解析、切分和摘要生成
    """

    def __init__(self,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None,
                 llm_model: Optional[str] = None):
        """
        初始化文本处理器

        Args:
            api_key: API密钥（None时从配置获取）
            base_url: API基础URL（None时从配置获取）
            llm_model: 大语言模型名称（None时从配置获取）
        """
        # 导入配置
        from config import api_config

        # 使用传入参数或配置默认值
        self.api_key = api_key if api_key is not None else api_config.openai_api_key
        self.base_url = base_url if base_url is not None else api_config.openai_base_url
        self.llm_model = llm_model if llm_model is not None else api_config.openai_model

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

        # 初始化工具类
        self.parser = Parser()
        self.splitter = Splitter()
        self.summarizer = Summarizer(
            api_key=self.api_key,
            base_url=self.base_url,
            model=self.llm_model
        )

        self.logger.info("文本处理器初始化完成")

    def process_file(self, file_path: str,
                     generate_summary: bool = True,
                     chunk_size: int = 1000,
                     overlap: int = 100) -> Dict[str, Any]:
        """
        处理单个文件，进行解析、切分和摘要生成

        Args:
            file_path: 文件路径
            generate_summary: 是否生成摘要
            chunk_size: 细分块大小
            overlap: 重叠大小

        Returns:
            处理结果字典
        """
        start_time = time.time()
        file_path = Path(file_path)
        source_name = file_path.stem

        self.logger.info(f"开始处理文件: {file_path.name}")

        try:
            # 1. 文件解析
            self.logger.info("步骤1: 文件解析")
            parse_result = self.parser.parse(file_path)
            text = parse_result['text']
            metadata = parse_result['metadata']

            # 2. 章节切分
            self.logger.info("步骤2: 章节切分")
            headings = metadata.get('headings', None)
            segments = self.splitter.split_to_chapters(
                text, source_name, use_headings=headings
            )

            # 3. 生成摘要（可选）
            if generate_summary:
                self.logger.info("步骤3: 生成摘要")
                segments = self.summarizer.summarize_segments(segments, source_name)

                # 生成整体摘要
                summaries = [seg.get('summary', '') for seg in segments if seg.get('summary')]
                if summaries:
                    overall_summary = self.summarizer.create_overall_summary(summaries, source_name)
                else:
                    overall_summary = None
            else:
                overall_summary = None

            # 4. 细粒度切分
            self.logger.info("步骤4: 细粒度切分")
            all_chunks = []
            for i, segment in enumerate(segments):
                chunks = self.splitter.split_to_chunks(
                    segment['text'], source_name, i + 1, chunk_size, overlap
                )
                all_chunks.extend(chunks)

            # 5. 保存处理结果
            result = {
                'source_file': str(file_path),
                'source_name': source_name,
                'parse_metadata': metadata,
                'segments': segments,
                'chunks': all_chunks,
                'overall_summary': overall_summary,
                'processing_time': time.time() - start_time,
                'settings': {
                    'generate_summary': generate_summary,
                    'chunk_size': chunk_size,
                    'overlap': overlap
                }
            }

            # 保存完整结果
            result_file = Path("processed_results") / f"{source_name}_text_result.json"
            result_file.parent.mkdir(exist_ok=True)

            # 创建可序列化的结果
            serializable_result = self._make_serializable(result)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, ensure_ascii=False, indent=2)

            self.logger.info(f"文件处理完成: {file_path.name}, 耗时: {result['processing_time']:.2f}秒")
            return result

        except Exception as e:
            self.logger.error(f"文件处理失败: {file_path.name}, 错误: {str(e)}")
            raise

    def process_directory(self, directory_path: str, **kwargs) -> List[Dict[str, Any]]:
        """
        批量处理目录中的文件

        Args:
            directory_path: 目录路径
            **kwargs: 传递给process_file的参数

        Returns:
            处理结果列表
        """
        directory = Path(directory_path)
        if not directory.exists():
            raise FileNotFoundError(f"目录不存在: {directory}")

        # 支持的文件扩展名
        supported_extensions = {'.pdf', '.docx', '.doc', '.txt'}

        # 查找支持的文件
        files = []
        for ext in supported_extensions:
            files.extend(directory.glob(f"*{ext}"))

        if not files:
            self.logger.warning(f"目录中没有找到支持的文件: {directory}")
            return []

        self.logger.info(f"找到{len(files)}个文件待处理")

        results = []
        for file_path in files:
            try:
                result = self.process_file(file_path, **kwargs)
                results.append(result)
            except Exception as e:
                self.logger.error(f"处理文件失败: {file_path.name}, 跳过")
                continue

        # 保存批量处理结果
        batch_result_file = Path("processed_results") / "batch_text_results.json"
        batch_result_file.parent.mkdir(exist_ok=True)

        serializable_results = [self._make_serializable(r) for r in results]
        with open(batch_result_file, 'w', encoding='utf-8') as f:
            json.dump(serializable_results, f, ensure_ascii=False, indent=2)

        self.logger.info(f"批量处理完成，成功处理{len(results)}个文件")
        return results

    def get_stats(self) -> Dict[str, Any]:
        """
        获取处理统计信息

        Returns:
            统计信息字典
        """
        return {
            'directories': {
                'parsed_text': len(list(Path("parsed_text").glob("*.txt"))),
                'segments': len(list(Path("segments").glob("*.txt"))),
                'summaries': len(list(Path("summaries").glob("*.txt"))),
                'chunks': len(list(Path("chunks").glob("*.txt")))
            },
            'processor_info': {
                'api_key_set': bool(self.api_key),
                'base_url': self.base_url,
                'llm_model': self.llm_model
            }
        }

    def _make_serializable(self, obj):
        """
        将对象转换为可序列化的格式

        Args:
            obj: 要转换的对象

        Returns:
            可序列化的对象
        """
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, 'tolist'):  # numpy数组
            return obj.tolist()
        elif isinstance(obj, Path):
            return str(obj)
        else:
            return obj


if __name__ == "__main__":
    # 示例用法
    processor = TextProcessor()

    print("文本处理器已创建")
    print("使用方法:")
    print("1. 处理单个文件: processor.process_file('文件路径')")
    print("2. 批量处理: processor.process_directory('目录路径')")
    print("3. 获取统计: processor.get_stats()")
