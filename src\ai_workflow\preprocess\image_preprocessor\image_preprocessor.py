"""
图像预处理器
负责将输入的图像文件拷贝到项目统一目录，并进行标准化命名
"""

import os
import shutil
import hashlib
from datetime import datetime
from typing import List, Dict, Optional
from PIL import Image
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ImagePreprocessor:
    """图像预处理器"""
    
    def __init__(self, input_dir: str = None, output_dir: str = None):
        """
        初始化图像预处理器
        
        Args:
            input_dir: 输入目录，默认为 src/ai_workflow/input/image
            output_dir: 输出目录，默认为 src/ai_workflow/output/preprocess/image
        """
        # 设置默认目录
        if input_dir is None:
            input_dir = "src/ai_workflow/input/image"
        if output_dir is None:
            output_dir = "src/ai_workflow/output/preprocess/image"
        
        self.input_dir = input_dir
        self.output_dir = output_dir
        
        # 创建目录
        os.makedirs(self.input_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 支持的图像格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif', '.webp'}
        
        # 处理记录
        self.processing_log = []
        
        logger.info(f"ImagePreprocessor initialized")
        logger.info(f"Input directory: {self.input_dir}")
        logger.info(f"Output directory: {self.output_dir}")
    
    def is_image_file(self, file_path: str) -> bool:
        """
        检查文件是否为支持的图像格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为图像文件
        """
        return os.path.splitext(file_path)[1].lower() in self.supported_formats
    
    def get_file_hash(self, file_path: str) -> str:
        """
        计算文件MD5哈希值
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件哈希值
        """
        hash_md5 = hashlib.md5()
        try:
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.error(f"Failed to calculate hash for {file_path}: {e}")
            return ""
    
    def get_image_info(self, image_path: str) -> Dict:
        """
        获取图像基本信息
        
        Args:
            image_path: 图像路径
            
        Returns:
            图像信息字典
        """
        try:
            with Image.open(image_path) as img:
                return {
                    "width": img.width,
                    "height": img.height,
                    "mode": img.mode,
                    "format": img.format,
                    "size_bytes": os.path.getsize(image_path)
                }
        except Exception as e:
            logger.error(f"Failed to get image info for {image_path}: {e}")
            return {}
    
    def generate_standard_filename(self, original_path: str, index: int = None) -> str:
        """
        生成标准化文件名
        
        Args:
            original_path: 原始文件路径
            index: 序号（可选）
            
        Returns:
            标准化文件名
        """
        # 获取原始文件名和扩展名
        original_name = os.path.splitext(os.path.basename(original_path))[0]
        extension = os.path.splitext(original_path)[1].lower()
        
        # 清理文件名，只保留字母数字和下划线
        clean_name = "".join(c if c.isalnum() or c in "_-" else "_" for c in original_name)
        clean_name = clean_name[:50]  # 限制长度
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 构建文件名
        if index is not None:
            filename = f"{clean_name}_{timestamp}_{index:04d}{extension}"
        else:
            filename = f"{clean_name}_{timestamp}{extension}"
        
        return filename
    
    def copy_image_to_input(self, source_path: str, target_filename: str = None) -> Optional[str]:
        """
        将图像文件拷贝到输入目录
        
        Args:
            source_path: 源文件路径
            target_filename: 目标文件名（可选）
            
        Returns:
            目标文件路径，失败返回None
        """
        if not os.path.exists(source_path):
            logger.error(f"Source file not found: {source_path}")
            return None
        
        if not self.is_image_file(source_path):
            logger.error(f"Not a supported image file: {source_path}")
            return None
        
        try:
            # 生成目标文件名
            if target_filename is None:
                target_filename = self.generate_standard_filename(source_path)
            
            target_path = os.path.join(self.input_dir, target_filename)
            
            # 如果目标文件已存在，添加序号
            counter = 1
            base_name, ext = os.path.splitext(target_filename)
            while os.path.exists(target_path):
                target_filename = f"{base_name}_{counter:03d}{ext}"
                target_path = os.path.join(self.input_dir, target_filename)
                counter += 1
            
            # 拷贝文件
            shutil.copy2(source_path, target_path)
            
            # 记录处理信息
            processing_info = {
                "source_path": source_path,
                "target_path": target_path,
                "target_filename": target_filename,
                "file_hash": self.get_file_hash(target_path),
                "image_info": self.get_image_info(target_path),
                "processed_time": datetime.now().isoformat()
            }
            
            self.processing_log.append(processing_info)
            
            logger.info(f"Image copied: {source_path} -> {target_path}")
            return target_path
            
        except Exception as e:
            logger.error(f"Failed to copy image {source_path}: {e}")
            return None
    
    def process_directory(self, source_dir: str) -> List[str]:
        """
        处理目录中的所有图像文件
        
        Args:
            source_dir: 源目录路径
            
        Returns:
            成功处理的文件路径列表
        """
        if not os.path.exists(source_dir):
            logger.error(f"Source directory not found: {source_dir}")
            return []
        
        processed_files = []
        image_files = []
        
        # 收集所有图像文件
        for root, _, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                if self.is_image_file(file_path):
                    image_files.append(file_path)
        
        logger.info(f"Found {len(image_files)} image files in {source_dir}")
        
        # 处理每个图像文件
        for i, image_path in enumerate(image_files):
            target_path = self.copy_image_to_input(image_path)
            if target_path:
                processed_files.append(target_path)
        
        logger.info(f"Successfully processed {len(processed_files)} images")
        return processed_files
    
    def process_file_list(self, file_paths: List[str]) -> List[str]:
        """
        处理文件路径列表
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            成功处理的文件路径列表
        """
        processed_files = []
        
        for file_path in file_paths:
            if self.is_image_file(file_path):
                target_path = self.copy_image_to_input(file_path)
                if target_path:
                    processed_files.append(target_path)
            else:
                logger.warning(f"Skipping non-image file: {file_path}")
        
        return processed_files
    
    def save_processing_log(self, log_path: str = None) -> str:
        """
        保存处理日志
        
        Args:
            log_path: 日志文件路径（可选）
            
        Returns:
            日志文件路径
        """
        if log_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_path = os.path.join(self.output_dir, f"processing_log_{timestamp}.json")
        
        try:
            import json
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "processing_summary": {
                        "total_processed": len(self.processing_log),
                        "input_directory": self.input_dir,
                        "output_directory": self.output_dir,
                        "timestamp": datetime.now().isoformat()
                    },
                    "processed_files": self.processing_log
                }, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Processing log saved to: {log_path}")
            return log_path
            
        except Exception as e:
            logger.error(f"Failed to save processing log: {e}")
            return ""
    
    def get_processing_summary(self) -> Dict:
        """
        获取处理摘要
        
        Returns:
            处理摘要字典
        """
        return {
            "total_processed": len(self.processing_log),
            "input_directory": self.input_dir,
            "output_directory": self.output_dir,
            "supported_formats": list(self.supported_formats),
            "last_processed": self.processing_log[-1]["processed_time"] if self.processing_log else None
        }
    
    def clear_input_directory(self) -> bool:
        """
        清空输入目录
        
        Returns:
            是否成功清空
        """
        try:
            for file in os.listdir(self.input_dir):
                file_path = os.path.join(self.input_dir, file)
                if os.path.isfile(file_path):
                    os.remove(file_path)
            
            logger.info(f"Input directory cleared: {self.input_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to clear input directory: {e}")
            return False


def main():
    """测试函数"""
    # 初始化预处理器
    preprocessor = ImagePreprocessor()
    
    # 显示摘要
    summary = preprocessor.get_processing_summary()
    print("Image Preprocessor Summary:")
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    # 测试处理（如果有测试图像）
    test_dir = "test_images"
    if os.path.exists(test_dir):
        print(f"\nProcessing test directory: {test_dir}")
        processed = preprocessor.process_directory(test_dir)
        print(f"Processed {len(processed)} images")
        
        # 保存日志
        log_path = preprocessor.save_processing_log()
        print(f"Log saved to: {log_path}")


if __name__ == "__main__":
    main()
