from PIL import Image

from transformers import CLIPProcessor, CLIPModel
model = CLIPModel.from_pretrained("D:\\Project\\python\\game_generation\\src\\ai_workflow\\models\\clip-vit-base-patch16")
processor = CLIPProcessor.from_pretrained("D:\\Project\\python\\game_generation\\src\\ai_workflow\\models\\clip-vit-base-patch16")
url = "D:\\Project\\python\\game_generation\\src\\ai_workflow\\input\\image\\769.jpg"
image = Image.open(url).convert("RGB")
inputs = processor(text=["a photo of a cat", "a photo of a dog"], images=image, return_tensors="pt", padding=True)
outputs = model(**inputs)
logits_per_image = outputs.logits_per_image # this is the image-text similarity score
probs = logits_per_image.softmax(dim=1) # we can take the softmax to get the label probabilities
print(outputs)
print(logits_per_image)
print(probs)