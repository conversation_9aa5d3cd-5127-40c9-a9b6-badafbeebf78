"""
AI-GAL 后端服务客户端示例
演示如何调用后端API接口
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class AIGALClient:
    """AI-GAL 后端服务客户端"""
    
    def __init__(self, base_url: str = "http://127.0.0.1:8080"):
        """
        初始化客户端
        
        Args:
            base_url: 后端服务基础URL
        """
        self.base_url = base_url
        self.api_base = f"{base_url}/api/v1"
    
    def call_service(self, service_name: str, params: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        同步调用服务
        
        Args:
            service_name: 服务名称
            params: 服务参数
            
        Returns:
            服务响应
        """
        if params is None:
            params = {}
        
        payload = {
            "service": service_name,
            "params": params
        }
        
        try:
            response = requests.post(
                f"{self.api_base}/call",
                json=payload,
                timeout=300  # 5分钟超时
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "status": "error",
                    "message": f"HTTP错误: {response.status_code}",
                    "data": None
                }
        except Exception as e:
            return {
                "status": "error",
                "message": f"请求异常: {str(e)}",
                "data": None
            }
    
    def call_service_async(self, service_name: str, params: Dict[str, Any] = None, 
                          callback_url: Optional[str] = None) -> str:
        """
        异步调用服务
        
        Args:
            service_name: 服务名称
            params: 服务参数
            callback_url: 回调URL
            
        Returns:
            任务ID
        """
        if params is None:
            params = {}
        
        payload = {
            "service": service_name,
            "params": params
        }
        
        if callback_url:
            payload["callback_url"] = callback_url
        
        try:
            response = requests.post(
                f"{self.api_base}/call-async",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return data.get("task_id")
            else:
                raise Exception(f"HTTP错误: {response.status_code}")
        except Exception as e:
            raise Exception(f"异步调用失败: {str(e)}")
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """
        查询任务状态
        
        Args:
            task_id: 任务ID
            
        Returns:
            任务状态信息
        """
        try:
            response = requests.get(
                f"{self.api_base}/task/{task_id}",
                timeout=10
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "status": "error",
                    "message": f"HTTP错误: {response.status_code}"
                }
        except Exception as e:
            return {
                "status": "error",
                "message": f"查询异常: {str(e)}"
            }
    
    def wait_for_task(self, task_id: str, timeout: int = 300) -> Dict[str, Any]:
        """
        等待异步任务完成
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            
        Returns:
            任务结果
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            status = self.get_task_status(task_id)
            
            if status.get("status") in ["completed", "failed", "cancelled"]:
                return status
            
            time.sleep(2)  # 每2秒查询一次
        
        return {
            "status": "timeout",
            "message": "任务执行超时"
        }
    
    def get_services(self) -> Dict[str, Any]:
        """获取所有可用服务"""
        try:
            response = requests.get(f"{self.api_base}/services", timeout=10)
            if response.status_code == 200:
                return response.json()
            else:
                return {
                    "status": "error",
                    "message": f"HTTP错误: {response.status_code}"
                }
        except Exception as e:
            return {
                "status": "error",
                "message": f"请求异常: {str(e)}"
            }

def example_sync_calls():
    """同步调用示例"""
    print("🔄 同步调用示例")
    print("=" * 30)
    
    client = AIGALClient()
    
    # 1. 测试连通性
    print("1. 测试连通性...")
    result = client.call_service("ping")
    print(f"   结果: {result.get('status')} - {result.get('message')}")
    
    # 2. 获取系统信息
    print("\n2. 获取系统信息...")
    result = client.call_service("get_system_info")
    if result.get("status") == "success":
        data = result.get("data", {})
        print(f"   系统: {data.get('title')}")
        print(f"   版本: {data.get('version')}")
    else:
        print(f"   失败: {result.get('message')}")
    
    # 3. 检查配置
    print("\n3. 检查配置...")
    result = client.call_service("check_configuration")
    if result.get("status") == "success":
        data = result.get("data", {})
        config_status = data.get("config_status", [])
        configured = sum(1 for item in config_status if item.get("status") == "已配置")
        print(f"   配置状态: {configured}/{len(config_status)} 已配置")
    else:
        print(f"   失败: {result.get('message')}")

def example_async_calls():
    """异步调用示例"""
    print("\n🔄 异步调用示例")
    print("=" * 30)
    
    client = AIGALClient()
    
    # 创建异步任务
    print("1. 创建异步任务...")
    try:
        task_id = client.call_service_async("ping")
        print(f"   任务ID: {task_id}")
        
        # 等待任务完成
        print("2. 等待任务完成...")
        result = client.wait_for_task(task_id, timeout=30)
        print(f"   任务状态: {result.get('status')}")
        
        if result.get("status") == "completed":
            task_result = result.get("result", {})
            print(f"   任务结果: {task_result.get('status')} - {task_result.get('message')}")
        
    except Exception as e:
        print(f"   异步调用失败: {e}")

def example_game_generation():
    """游戏生成示例"""
    print("\n🎮 游戏生成示例")
    print("=" * 30)
    
    client = AIGALClient()
    
    # 准备参数
    params = {
        "theme": "雷州石狗文化遗产",
        "material_paths": ["example_materials/雷州石狗文化.txt"],
        "reference_materials": ["石狗传说", "雷州历史"]
    }
    
    print("开始生成游戏...")
    print(f"主题: {params['theme']}")
    
    # 由于游戏生成可能需要很长时间，建议使用异步调用
    try:
        task_id = client.call_service_async("generate_complete_game", params)
        print(f"任务ID: {task_id}")
        print("任务已创建，可以通过以下方式查询状态:")
        print(f"curl http://127.0.0.1:8080/api/v1/task/{task_id}")
        
    except Exception as e:
        print(f"游戏生成失败: {e}")

def main():
    """主函数"""
    print("🎯 AI-GAL 后端服务客户端示例")
    print("=" * 50)
    
    # 运行示例
    example_sync_calls()
    example_async_calls()
    
    # 询问是否运行游戏生成示例
    response = input("\n是否运行游戏生成示例？(y/N): ").strip().lower()
    if response == 'y':
        example_game_generation()
    
    print("\n✅ 示例运行完成")

if __name__ == "__main__":
    main()
