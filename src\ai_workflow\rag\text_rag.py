"""
RAG系统模块
专注于向量化、检索和问答功能
"""

import logging
import json
from pathlib import Path
from typing import Dict, Any, List, Optional
import time
import sys

# 添加路径以导入其他模块
sys.path.append(str(Path(__file__).parent.parent.parent))

# 导入统一配置管理
from src.config import get_config, get_section

# 导入工具类
from tools.embedding_tool import EmbeddingTool
from tools.retriever import Retriever


class RAGSystem:
    """
    RAG系统，专注于向量化、检索和问答功能
    """

    def __init__(self,
                 use_local_embedding: Optional[bool] = None,
                 embedding_model: Optional[str] = None,
                 api_key: Optional[str] = None,
                 base_url: Optional[str] = None):
        """
        初始化RAG系统

        Args:
            use_local_embedding: 是否使用本地嵌入模型（None时从配置获取）
            embedding_model: 嵌入模型名称（None时从配置获取）
            api_key: API密钥（None时从配置获取）
            base_url: API基础URL（None时从配置获取）
        """
        # 获取RAG配置
        rag_config = get_section('rag', 'embedding')
        api_config = get_section('api', 'openai')

        # 使用传入参数或配置默认值
        self.use_local_embedding = use_local_embedding if use_local_embedding is not None else rag_config.get('use_local', True)
        self.embedding_model = embedding_model if embedding_model is not None else rag_config.get('model', 'Qwen/Qwen3-Embedding-0.6B')
        self.api_key = api_key if api_key is not None else api_config.get('api_key', '')
        self.base_url = base_url if base_url is not None else api_config.get('base_url', '')

        # 设置日志
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger(__name__)

        # 初始化工具类
        self.embedding_tool = EmbeddingTool(
            use_local=self.use_local_embedding,
            model_name=self.embedding_model,
            api_key=self.api_key,
            base_url=self.base_url
        )
        self.retriever = Retriever()

        self.logger.info("RAG系统初始化完成")

    def vectorize_segments(self, segments: List[Dict[str, Any]],
                           source_name: str,
                           use_summary: bool = False) -> List[Dict[str, Any]]:
        """
        为章节生成向量

        Args:
            segments: 章节列表
            source_name: 源文件名
            use_summary: 是否使用摘要生成向量

        Returns:
            包含向量的章节列表
        """
        self.logger.info(f"开始为章节生成向量: {source_name}")

        try:
            # 为章节生成向量
            segments_with_embeddings = self.embedding_tool.embed_segments(
                segments, source_name, use_summary=use_summary
            )

            self.logger.info(f"章节向量化完成: {source_name}")
            return segments_with_embeddings

        except Exception as e:
            self.logger.error(f"章节向量化失败: {source_name}, 错误: {str(e)}")
            raise

    def vectorize_chunks(self, chunks: List[Dict[str, Any]],
                         source_name: str) -> List[Dict[str, Any]]:
        """
        为细分块生成向量

        Args:
            chunks: 细分块列表
            source_name: 源文件名

        Returns:
            包含向量的块列表
        """
        self.logger.info(f"开始为细分块生成向量: {source_name}")

        try:
            # 提取文本
            chunk_texts = [chunk['text'] for chunk in chunks]

            # 生成向量
            chunk_embeddings = self.embedding_tool.embed(chunk_texts)

            # 更新块信息
            chunks_with_embeddings = []
            for chunk, embedding in zip(chunks, chunk_embeddings):
                chunk_with_embedding = chunk.copy()
                chunk_with_embedding['embedding'] = embedding.tolist()
                chunk_with_embedding['embedding_dim'] = len(embedding)
                chunks_with_embeddings.append(chunk_with_embedding)

            # 保存块向量
            chunk_metadata = [
                {
                    'index': i,
                    'source_name': source_name,
                    'type': 'chunk',
                    'segment_index': chunk.get('segment_index', 0),
                    'chunk_index': chunk.get('index', i)
                }
                for i, chunk in enumerate(chunks)
            ]

            self.embedding_tool.save_embeddings(
                chunk_embeddings, chunk_metadata, f"{source_name}_chunks"
            )

            self.logger.info(f"细分块向量化完成: {source_name}, 共{len(chunks)}个块")
            return chunks_with_embeddings

        except Exception as e:
            self.logger.error(f"细分块向量化失败: {source_name}, 错误: {str(e)}")
            raise

    def build_index(self, chunks_with_embeddings: List[Dict[str, Any]],
                    source_name: str) -> None:
        """
        构建检索索引

        Args:
            chunks_with_embeddings: 包含向量的块列表
            source_name: 源文件名
        """
        self.logger.info(f"开始构建检索索引: {source_name}")

        try:
            # 提取向量和元数据
            embeddings = []
            metadata = []

            for i, chunk in enumerate(chunks_with_embeddings):
                if 'embedding' in chunk:
                    embeddings.append(chunk['embedding'])
                    metadata.append({
                        'index': i,
                        'source_name': source_name,
                        'segment_index': chunk.get('segment_index', 0),
                        'chunk_index': chunk.get('index', i),
                        'text': chunk['text'][:200] + '...' if len(chunk['text']) > 200 else chunk['text'],
                        'char_count': chunk.get('char_count', len(chunk['text']))
                    })

            if embeddings:
                import numpy as np
                embeddings_array = np.array(embeddings)

                # 添加到检索器
                self.retriever.add_vectors(embeddings_array, metadata)

                self.logger.info(f"检索索引构建完成: {source_name}, 共{len(embeddings)}个向量")
            else:
                self.logger.warning(f"没有找到向量数据: {source_name}")

        except Exception as e:
            self.logger.error(f"构建检索索引失败: {source_name}, 错误: {str(e)}")
            raise

    def search(self, query: str, top_k: int = 5) -> List[Dict[str, Any]]:
        """
        搜索相关内容

        Args:
            query: 查询文本
            top_k: 返回结果数量

        Returns:
            搜索结果列表
        """
        try:
            results = self.retriever.search_text(query, self.embedding_tool, top_k)
            self.logger.info(f"搜索完成: '{query}', 返回{len(results)}个结果")
            return results
        except Exception as e:
            self.logger.error(f"搜索失败: {str(e)}")
            raise

    def process_text_data(self, text_result: Dict[str, Any],
                          vectorize_segments: bool = True,
                          vectorize_chunks: bool = True,
                          build_index: bool = True) -> Dict[str, Any]:
        """
        处理文本处理结果，进行向量化和索引构建

        Args:
            text_result: 文本处理结果
            vectorize_segments: 是否向量化章节
            vectorize_chunks: 是否向量化细分块
            build_index: 是否构建索引

        Returns:
            RAG处理结果
        """
        start_time = time.time()
        source_name = text_result['source_name']

        self.logger.info(f"开始RAG处理: {source_name}")

        try:
            rag_result = {
                'source_name': source_name,
                'vectorize_segments': vectorize_segments,
                'vectorize_chunks': vectorize_chunks,
                'build_index': build_index,
                'processing_time': 0
            }

            # 向量化章节
            if vectorize_segments and 'segments' in text_result:
                segments_with_embeddings = self.vectorize_segments(
                    text_result['segments'],
                    source_name,
                    use_summary='overall_summary' in text_result and text_result['overall_summary']
                )
                rag_result['segments_with_embeddings'] = segments_with_embeddings

            # 向量化细分块
            chunks_with_embeddings = None
            if vectorize_chunks and 'chunks' in text_result:
                chunks_with_embeddings = self.vectorize_chunks(
                    text_result['chunks'],
                    source_name
                )
                rag_result['chunks_with_embeddings'] = chunks_with_embeddings

            # 构建索引
            if build_index and chunks_with_embeddings:
                self.build_index(chunks_with_embeddings, source_name)
                rag_result['index_built'] = True
            else:
                rag_result['index_built'] = False

            rag_result['processing_time'] = time.time() - start_time

            # 保存RAG结果
            result_file = Path("processed_results") / f"{source_name}_rag_result.json"
            result_file.parent.mkdir(exist_ok=True)

            # 创建可序列化的结果（移除numpy数组）
            serializable_result = self._make_serializable(rag_result)
            with open(result_file, 'w', encoding='utf-8') as f:
                json.dump(serializable_result, f, ensure_ascii=False, indent=2)

            self.logger.info(f"RAG处理完成: {source_name}, 耗时: {rag_result['processing_time']:.2f}秒")
            return rag_result

        except Exception as e:
            self.logger.error(f"RAG处理失败: {source_name}, 错误: {str(e)}")
            raise

    def save_index(self, filename: str = "main_index") -> str:
        """
        保存检索器索引

        Args:
            filename: 索引文件名

        Returns:
            保存路径
        """
        return self.retriever.save_index(filename)

    def load_index(self, filename: str = "main_index") -> None:
        """
        加载检索器索引

        Args:
            filename: 索引文件名
        """
        self.retriever.load_index(filename)

    def get_stats(self) -> Dict[str, Any]:
        """
        获取RAG系统统计信息

        Returns:
            统计信息字典
        """
        return {
            'retriever_stats': self.retriever.get_stats(),
            'embedding_info': {
                'use_local': self.use_local_embedding,
                'model': self.embedding_model,
                'api_key_set': bool(self.api_key)
            },
            'directories': {
                'embeddings': len(list(Path("embeddings").glob("*.npz")))
            }
        }

    def _make_serializable(self, obj):
        """
        将对象转换为可序列化的格式

        Args:
            obj: 要转换的对象

        Returns:
            可序列化的对象
        """
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif hasattr(obj, 'tolist'):  # numpy数组
            return obj.tolist()
        elif isinstance(obj, Path):
            return str(obj)
        else:
            return obj


if __name__ == "__main__":
    # 示例用法
    rag_system = RAGSystem()

    print("RAG系统已创建")
    print("使用方法:")
    print("1. 处理文本数据: rag_system.process_text_data(text_result)")
    print("2. 搜索: rag_system.search('查询文本')")
    print("3. 保存索引: rag_system.save_index()")
    print("4. 加载索引: rag_system.load_index()")
    print("5. 获取统计: rag_system.get_stats()")
