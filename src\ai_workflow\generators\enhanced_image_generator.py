"""
增强图像生成器
集成CLIP检索和IP-Adapter的图像生成功能到主生成器架构中
"""

import os
import sys
from typing import Optional, Dict, Any, List, Tuple
from PIL import Image
import logging

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入配置管理
from src.config import get_config, get_section

# 导入增强图像生成模块
from src.ai_workflow.preprocess.image_preprocessor.enhanced_image_generator import EnhancedImageGenerator as CoreEnhancedGenerator

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EnhancedImageGenerator:
    """增强图像生成器 - 主生成器架构适配器"""
    
    def __init__(self):
        """初始化增强图像生成器"""
        # 获取配置
        self.config = get_section('generators', 'image_generation.enhanced_generation')
        
        # 检查是否启用
        self.enabled = self.config.get('enabled', True)
        if not self.enabled:
            logger.warning("Enhanced image generation is disabled in config")
            return
        
        # 初始化核心生成器
        try:
            self.core_generator = CoreEnhancedGenerator(
                clip_model_path=self.config.get('clip_model_path'),
                sd_model_path=self.config.get('sd_model_path'),
                similarity_threshold=self.config.get('similarity_threshold', 0.2),
                ip_adapter_scale=self.config.get('ip_adapter_scale', 0.5),
                auto_build_index=self.config.get('auto_build_index', True)
            )
            logger.info("Enhanced Image Generator initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Enhanced Image Generator: {e}")
            self.enabled = False
            raise
    
    def generate_image(
        self,
        prompt: str,
        image_name: str = None,
        mode: str = 'character',
        use_enhancement: bool = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        生成图像 - 兼容现有接口
        
        Args:
            prompt: 文本提示
            image_name: 图像名称
            mode: 生成模式 ('character', 'background')
            use_enhancement: 是否使用图像增强
            **kwargs: 其他生成参数
            
        Returns:
            生成结果字典
        """
        if not self.enabled:
            return {"status": "error", "message": "Enhanced generation is disabled"}
        
        try:
            # 设置默认参数
            if use_enhancement is None:
                use_enhancement = self.config.get('use_image_enhancement', True)
            
            # 根据模式设置图像尺寸
            generation_settings = self.config.get('generation_settings', {})
            if mode == 'background':
                width = kwargs.get('width', 960)
                height = kwargs.get('height', 540)
                prompt = f"{prompt}, (no_human), landscape, background"
            else:
                width = kwargs.get('width', generation_settings.get('width', 512))
                height = kwargs.get('height', generation_settings.get('height', 768))
                prompt = f"{prompt}, (upper_body), solo, character"
            
            # 生成图像
            generated_image, generation_info = self.core_generator.generate_enhanced_image(
                text_prompt=prompt,
                width=width,
                height=height,
                num_inference_steps=kwargs.get('num_inference_steps', generation_settings.get('num_inference_steps', 25)),
                guidance_scale=kwargs.get('guidance_scale', generation_settings.get('guidance_scale', 7.5)),
                seed=kwargs.get('seed'),
                use_image_enhancement=use_enhancement,
                save_image=True,
                filename_prefix=image_name
            )
            
            return {
                "status": "success",
                "image": generated_image,
                "output_path": generation_info.get("output_path"),
                "generation_info": generation_info
            }
            
        except Exception as e:
            logger.error(f"Failed to generate enhanced image: {e}")
            return {"status": "error", "message": str(e)}
    
    def generate_batch(
        self,
        prompts: List[str],
        mode: str = 'character',
        use_enhancement: bool = None,
        **kwargs
    ) -> List[Dict[str, Any]]:
        """
        批量生成图像
        
        Args:
            prompts: 文本提示列表
            mode: 生成模式
            use_enhancement: 是否使用图像增强
            **kwargs: 其他生成参数
            
        Returns:
            生成结果列表
        """
        if not self.enabled:
            return [{"status": "error", "message": "Enhanced generation is disabled"}] * len(prompts)
        
        results = []
        for i, prompt in enumerate(prompts):
            result = self.generate_image(
                prompt=prompt,
                image_name=f"batch_{i+1:03d}",
                mode=mode,
                use_enhancement=use_enhancement,
                **kwargs
            )
            results.append(result)
        
        return results
    
    def rebuild_index(self) -> Dict[str, Any]:
        """
        重新构建图像索引
        
        Returns:
            操作结果
        """
        if not self.enabled:
            return {"status": "error", "message": "Enhanced generation is disabled"}
        
        try:
            success = self.core_generator.rebuild_index()
            return {
                "status": "success" if success else "error",
                "message": "Index rebuilt successfully" if success else "Failed to rebuild index"
            }
        except Exception as e:
            logger.error(f"Failed to rebuild index: {e}")
            return {"status": "error", "message": str(e)}
    
    def update_settings(self, **settings) -> Dict[str, Any]:
        """
        更新生成器设置
        
        Args:
            **settings: 设置参数
            
        Returns:
            操作结果
        """
        if not self.enabled:
            return {"status": "error", "message": "Enhanced generation is disabled"}
        
        try:
            self.core_generator.update_settings(
                similarity_threshold=settings.get('similarity_threshold'),
                ip_adapter_scale=settings.get('ip_adapter_scale')
            )
            
            return {"status": "success", "message": "Settings updated successfully"}
        except Exception as e:
            logger.error(f"Failed to update settings: {e}")
            return {"status": "error", "message": str(e)}
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        获取系统信息
        
        Returns:
            系统信息
        """
        if not self.enabled:
            return {"status": "disabled", "message": "Enhanced generation is disabled"}
        
        try:
            return self.core_generator.get_system_info()
        except Exception as e:
            logger.error(f"Failed to get system info: {e}")
            return {"status": "error", "message": str(e)}
    
    def search_similar_images(self, text: str, top_k: int = 5) -> Dict[str, Any]:
        """
        搜索相似图像
        
        Args:
            text: 查询文本
            top_k: 返回前k个结果
            
        Returns:
            搜索结果
        """
        if not self.enabled:
            return {"status": "error", "message": "Enhanced generation is disabled"}
        
        try:
            results = self.core_generator.retriever.search_similar_images(text, top_k)
            return {
                "status": "success",
                "results": [
                    {
                        "image_path": path,
                        "similarity_score": score,
                        "image_name": os.path.basename(path)
                    }
                    for path, score in results
                ]
            }
        except Exception as e:
            logger.error(f"Failed to search similar images: {e}")
            return {"status": "error", "message": str(e)}


# 兼容性函数，保持与现有代码的兼容性
def generate_image(prompt: str, image_name: str = None, mode: str = 'character') -> str:
    """
    兼容性函数 - 生成图像
    
    Args:
        prompt: 文本提示
        image_name: 图像名称
        mode: 生成模式
        
    Returns:
        生成状态 ("ok" 或 "error")
    """
    try:
        generator = EnhancedImageGenerator()
        result = generator.generate_image(prompt, image_name, mode)
        return "ok" if result["status"] == "success" else "error"
    except Exception as e:
        logger.error(f"Compatibility function failed: {e}")
        return "error"


def main():
    """测试函数"""
    try:
        # 初始化生成器
        generator = EnhancedImageGenerator()
        
        if not generator.enabled:
            print("Enhanced generation is disabled")
            return
        
        # 显示系统信息
        info = generator.get_system_info()
        print("System Info:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 测试搜索功能
        print("\nTesting image search...")
        search_result = generator.search_similar_images("传统花纹", top_k=3)
        print(f"Search result: {search_result}")
        
        # 测试生成功能
        print("\nTesting image generation...")
        result = generator.generate_image(
            prompt="传统中国花纹，精美刺绣，高质量",
            image_name="test_enhanced",
            mode="character"
        )
        print(f"Generation result: {result}")
        
    except Exception as e:
        print(f"Test failed: {e}")


if __name__ == "__main__":
    main()
