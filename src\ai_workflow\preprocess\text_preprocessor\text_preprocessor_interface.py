"""
文本预处理器接口
提供统一的文本预处理接口，输出到标准目录
"""

import os
import shutil
import json
from datetime import datetime
from typing import List, Dict, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextPreprocessorInterface:
    """文本预处理器接口"""
    
    def __init__(self, output_dir: str = None):
        """
        初始化文本预处理器接口
        
        Args:
            output_dir: 输出目录，默认为 src/ai_workflow/output/preprocess/text
        """
        # 设置默认目录
        if output_dir is None:
            output_dir = "src/ai_workflow/output/preprocess/text"
        
        self.output_dir = output_dir
        
        # 创建目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 支持的文本格式
        self.supported_formats = {'.txt', '.pdf', '.docx', '.doc', '.md'}
        
        # 处理记录
        self.processing_log = []
        
        logger.info(f"TextPreprocessorInterface initialized")
        logger.info(f"Output directory: {self.output_dir}")
    
    def is_text_file(self, file_path: str) -> bool:
        """
        检查文件是否为支持的文本格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为文本文件
        """
        return os.path.splitext(file_path)[1].lower() in self.supported_formats
    
    def extract_text_from_file(self, file_path: str) -> str:
        """
        从文件中提取文本内容
        
        Args:
            file_path: 文件路径
            
        Returns:
            提取的文本内容
        """
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            return ""
        
        file_ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if file_ext == '.txt' or file_ext == '.md':
                # 处理纯文本文件
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            elif file_ext == '.pdf':
                # 处理PDF文件
                return self._extract_from_pdf(file_path)
            
            elif file_ext in ['.docx', '.doc']:
                # 处理Word文档
                return self._extract_from_word(file_path)
            
            else:
                logger.warning(f"Unsupported file format: {file_ext}")
                return ""
                
        except Exception as e:
            logger.error(f"Failed to extract text from {file_path}: {e}")
            return ""
    
    def _extract_from_pdf(self, pdf_path: str) -> str:
        """从PDF文件提取文本"""
        try:
            import PyPDF2
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except ImportError:
            logger.warning("PyPDF2 not installed, cannot process PDF files")
            return f"PDF文件: {os.path.basename(pdf_path)} (需要安装PyPDF2)"
        except Exception as e:
            logger.error(f"Failed to extract PDF text: {e}")
            return f"PDF文件: {os.path.basename(pdf_path)} (提取失败)"
    
    def _extract_from_word(self, word_path: str) -> str:
        """从Word文档提取文本"""
        try:
            import docx
            doc = docx.Document(word_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text
        except ImportError:
            logger.warning("python-docx not installed, cannot process Word files")
            return f"Word文档: {os.path.basename(word_path)} (需要安装python-docx)"
        except Exception as e:
            logger.error(f"Failed to extract Word text: {e}")
            return f"Word文档: {os.path.basename(word_path)} (提取失败)"
    
    def process_text_file(self, file_path: str) -> Optional[str]:
        """
        处理单个文本文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            输出文件路径，失败返回None
        """
        if not self.is_text_file(file_path):
            logger.error(f"Not a supported text file: {file_path}")
            return None
        
        try:
            # 提取文本内容
            text_content = self.extract_text_from_file(file_path)
            if not text_content.strip():
                logger.warning(f"No text content extracted from: {file_path}")
                return None
            
            # 生成输出文件名
            base_name = os.path.splitext(os.path.basename(file_path))[0]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_filename = f"{base_name}_{timestamp}.txt"
            output_path = os.path.join(self.output_dir, output_filename)
            
            # 确保文件名唯一
            counter = 1
            while os.path.exists(output_path):
                output_filename = f"{base_name}_{timestamp}_{counter:03d}.txt"
                output_path = os.path.join(self.output_dir, output_filename)
                counter += 1
            
            # 保存处理后的文本
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(text_content)
            
            # 记录处理信息
            processing_info = {
                "source_path": file_path,
                "output_path": output_path,
                "output_filename": output_filename,
                "text_length": len(text_content),
                "processed_time": datetime.now().isoformat()
            }
            
            self.processing_log.append(processing_info)
            
            logger.info(f"Text file processed: {file_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"Failed to process text file {file_path}: {e}")
            return None
    
    def process_directory(self, source_dir: str) -> List[str]:
        """
        处理目录中的所有文本文件
        
        Args:
            source_dir: 源目录路径
            
        Returns:
            成功处理的文件路径列表
        """
        if not os.path.exists(source_dir):
            logger.error(f"Source directory not found: {source_dir}")
            return []
        
        processed_files = []
        text_files = []
        
        # 收集所有文本文件
        for root, _, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                if self.is_text_file(file_path):
                    text_files.append(file_path)
        
        logger.info(f"Found {len(text_files)} text files in {source_dir}")
        
        # 处理每个文本文件
        for text_path in text_files:
            output_path = self.process_text_file(text_path)
            if output_path:
                processed_files.append(output_path)
        
        logger.info(f"Successfully processed {len(processed_files)} text files")
        return processed_files
    
    def process_file_list(self, file_paths: List[str]) -> List[str]:
        """
        处理文件路径列表
        
        Args:
            file_paths: 文件路径列表
            
        Returns:
            成功处理的文件路径列表
        """
        processed_files = []
        
        for file_path in file_paths:
            if self.is_text_file(file_path):
                output_path = self.process_text_file(file_path)
                if output_path:
                    processed_files.append(output_path)
            else:
                logger.warning(f"Skipping non-text file: {file_path}")
        
        return processed_files
    
    def save_processing_log(self, log_path: str = None) -> str:
        """
        保存处理日志
        
        Args:
            log_path: 日志文件路径（可选）
            
        Returns:
            日志文件路径
        """
        if log_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_path = os.path.join(self.output_dir, f"text_processing_log_{timestamp}.json")
        
        try:
            with open(log_path, 'w', encoding='utf-8') as f:
                json.dump({
                    "processing_summary": {
                        "total_processed": len(self.processing_log),
                        "output_directory": self.output_dir,
                        "timestamp": datetime.now().isoformat()
                    },
                    "processed_files": self.processing_log
                }, f, ensure_ascii=False, indent=2)
            
            logger.info(f"Text processing log saved to: {log_path}")
            return log_path
            
        except Exception as e:
            logger.error(f"Failed to save processing log: {e}")
            return ""
    
    def get_processing_summary(self) -> Dict:
        """
        获取处理摘要
        
        Returns:
            处理摘要字典
        """
        return {
            "total_processed": len(self.processing_log),
            "output_directory": self.output_dir,
            "supported_formats": list(self.supported_formats),
            "last_processed": self.processing_log[-1]["processed_time"] if self.processing_log else None
        }


def main():
    """测试函数"""
    # 初始化预处理器
    preprocessor = TextPreprocessorInterface()
    
    # 显示摘要
    summary = preprocessor.get_processing_summary()
    print("Text Preprocessor Interface Summary:")
    for key, value in summary.items():
        print(f"  {key}: {value}")


if __name__ == "__main__":
    main()
