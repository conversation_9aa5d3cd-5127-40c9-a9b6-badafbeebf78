"""
素材预处理模块
实现文本分段、摘要生成(SAD)和图像语义分析(VSI)功能
"""

import os
import json
from PIL import Image
from typing import List, Dict
import hashlib
import sys

# 添加路径以导入其他模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))

# 导入统一配置管理
from src.config import get_config, get_section

from src.ai_workflow.preprocess.text_preprocessor.text_preprocessor import TextProcessor
from text_preprocessor import text_preprocessor

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

class MaterialPreprocessor:
    def __init__(self):
        """初始化素材预处理器"""
        # 获取预处理配置
        self.preprocess_config = get_section('preprocessor', 'text_processing')

        self.text_preprocessor = TextProcessor()

        # 创建输出目录
        sad_output_dir = get_config('rag', 'sad_processing.output_directory', 'SAD')
        vsi_output_dir = get_config('rag', 'vsi_processing.output_directory', 'VSI')

        self.sad_dir = os.path.join(game_directory, "data", sad_output_dir)
        self.vsi_dir = os.path.join(game_directory, "data", vsi_output_dir)
        os.makedirs(self.sad_dir, exist_ok=True)
        os.makedirs(self.vsi_dir, exist_ok=True)

    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """从PDF文件提取文本"""
        if not self.enable_pdf:
            return ""
        
        try:
            with open(pdf_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text = ""
                for page in pdf_reader.pages:
                    text += page.extract_text() + "\n"
                return text
        except Exception as e:
            print(f"PDF提取失败 {pdf_path}: {e}")
            return ""

    def analyze_image_with_clip(self, image_path: str) -> str:
        """使用CLIP-Interrogator分析图像"""
        # todo 完善图像反推VSI的方法
        # 这里需要集成CLIP-Interrogator
        # 由于CLIP-Interrogator需要特定的环境配置，这里提供一个简化版本
        try:
            # 简化版本：使用GPT-4V或其他视觉模型分析图像
            # 实际实现中应该使用CLIP-Interrogator
            return self.analyze_image_simple(image_path)
        except Exception as e:
            print(f"图像分析失败 {image_path}: {e}")
            return f"图像文件: {os.path.basename(image_path)}"

    def analyze_image_simple(self, image_path: str) -> str:
        """简化的图像分析方法"""
        # 获取图像基本信息
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                mode = img.mode
                
            filename = os.path.basename(image_path)
            description = f"图像文件: {filename}, 尺寸: {width}x{height}, 模式: {mode}"
            
            # 这里可以添加更复杂的图像分析逻辑
            # 例如调用在线图像识别API或本地模型
            
            return description
        except Exception as e:
            print(f"图像分析失败: {e}")
            return f"图像文件: {os.path.basename(image_path)}"

    def process_image_material(self, image_path: str) -> str:
        """处理图像素材，生成VSI"""
        print(f"处理图像素材: {image_path}")
        
        # 1. 图像分析
        description = self.analyze_image_with_clip(image_path)
        
        # 2. 生成VSI条目
        image_hash = self.get_file_hash(image_path)
        vsi_entry = {
            "image_path": image_path,
            "image_hash": image_hash,
            "description": description,
            "filename": os.path.basename(image_path),
            "processed_time": self.get_current_time()
        }
        
        # 3. 保存到VSI
        vsi_filename = f"{os.path.splitext(os.path.basename(image_path))[0]}_VSI.json"
        vsi_path = os.path.join(self.vsi_dir, vsi_filename)
        with open(vsi_path, 'w', encoding='utf-8') as f:
            json.dump(vsi_entry, f, ensure_ascii=False, indent=2)
        
        print(f"VSI文件已保存: {vsi_path}")
        return vsi_path

    def get_file_hash(self, file_path: str) -> str:
        """计算文件哈希值"""
        hash_md5 = hashlib.md5()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_md5.update(chunk)
        return hash_md5.hexdigest()

    def get_current_time(self) -> str:
        """获取当前时间字符串"""
        import datetime
        return datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def process_materials(self, material_paths: List[str]) -> Dict[str, List[str]]:
        """批量处理素材"""
        results = {
            "sad_files": [],
            "vsi_files": []
        }
        
        for path in material_paths:
            if not os.path.exists(path):
                print(f"文件不存在: {path}")
                continue
            
            file_ext = os.path.splitext(path)[1].lower()
            
            if file_ext == '.pdf':
                # 处理PDF文件
                text_content = self.extract_text_from_pdf(path)
                if text_content:
                    sad_path =
                    results["sad_files"].append(sad_path)
            elif file_ext == '.txt':
                # 处理文本文件
                with open(path, 'r', encoding='utf-8') as f:
                    text_content = f.read()
                sad_path =
                results["sad_files"].append(sad_path)
            elif file_ext in ['.jpg', '.jpeg', '.png', '.bmp', '.gif']:
                # 处理图像文件
                vsi_path = self.process_image_material(path)
                results["vsi_files"].append(vsi_path)
            else:
                print(f"不支持的文件类型: {path}")
        
        return results
