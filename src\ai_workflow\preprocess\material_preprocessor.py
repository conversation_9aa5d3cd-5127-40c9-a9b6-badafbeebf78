"""
素材预处理模块
简化版本：对输入目录进行分类，调用对应的预处理器
"""

import os
import shutil
from typing import List, Dict, Tuple
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import renpy
    game_directory = renpy.config.gamedir
except:
    game_directory = os.getcwd()

class ImagePreprocessor:
    """图像预处理器 - 将图像拷贝到统一目录并重命名"""

    def __init__(self):
        """初始化图像预处理器"""
        # 设置输入输出目录
        self.input_dir = os.path.join(game_directory, "src/ai_workflow/input/image")
        self.output_dir = os.path.join(game_directory, "src/ai_workflow/output/preprocess/image")

        # 创建目录
        os.makedirs(self.input_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)

        # 支持的图像格式
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif', '.webp'}

        logger.info(f"Image preprocessor initialized")
        logger.info(f"Input directory: {self.input_dir}")
        logger.info(f"Output directory: {self.output_dir}")

    def is_image_file(self, file_path: str) -> bool:
        """检查文件是否为支持的图像格式"""
        return os.path.splitext(file_path)[1].lower() in self.supported_formats

    def copy_and_rename_image(self, source_path: str, index: int) -> str:
        """拷贝图像到输入目录并重命名"""
        if not self.is_image_file(source_path):
            raise ValueError(f"Unsupported image format: {source_path}")

        # 获取文件扩展名
        _, ext = os.path.splitext(source_path)

        # 生成新文件名
        new_filename = f"image_{index:04d}{ext.lower()}"
        dest_path = os.path.join(self.input_dir, new_filename)

        # 拷贝文件
        shutil.copy2(source_path, dest_path)
        logger.info(f"Copied {os.path.basename(source_path)} -> {new_filename}")

        return dest_path

    def process_images(self, image_paths: List[str]) -> List[str]:
        """批量处理图像"""
        processed_paths = []

        for i, image_path in enumerate(image_paths):
            if not os.path.exists(image_path):
                logger.warning(f"Image file not found: {image_path}")
                continue

            if not self.is_image_file(image_path):
                logger.warning(f"Unsupported image format: {image_path}")
                continue

            try:
                dest_path = self.copy_and_rename_image(image_path, i + 1)
                processed_paths.append(dest_path)
            except Exception as e:
                logger.error(f"Failed to process image {image_path}: {e}")

        logger.info(f"Processed {len(processed_paths)} images")
        return processed_paths

    def process_directory(self, source_dir: str) -> List[str]:
        """处理目录中的所有图像"""
        if not os.path.exists(source_dir):
            logger.error(f"Source directory not found: {source_dir}")
            return []

        # 收集所有图像文件
        image_files = []
        for root, _, files in os.walk(source_dir):
            for file in files:
                file_path = os.path.join(root, file)
                if self.is_image_file(file_path):
                    image_files.append(file_path)

        logger.info(f"Found {len(image_files)} image files in {source_dir}")
        return self.process_images(image_files)


class MaterialPreprocessor:
    """简化的素材预处理器 - 分类并调用对应的预处理器"""

    def __init__(self):
        """初始化素材预处理器"""
        # 创建输出目录
        self.output_dir = os.path.join(game_directory, "src/ai_workflow/output/preprocess")
        self.text_output_dir = os.path.join(self.output_dir, "text")
        self.image_output_dir = os.path.join(self.output_dir, "image")

        os.makedirs(self.text_output_dir, exist_ok=True)
        os.makedirs(self.image_output_dir, exist_ok=True)

        # 初始化子预处理器
        self.image_preprocessor = ImagePreprocessor()

        # 支持的文件格式
        self.text_formats = {'.txt', '.md', '.pdf', '.docx', '.doc'}
        self.image_formats = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.gif', '.webp'}

        logger.info("Material preprocessor initialized")

    def classify_files(self, file_paths: List[str]) -> Tuple[List[str], List[str]]:
        """分类文件为文本和图像"""
        text_files = []
        image_files = []

        for file_path in file_paths:
            if not os.path.exists(file_path):
                logger.warning(f"File not found: {file_path}")
                continue

            _, ext = os.path.splitext(file_path)
            ext_lower = ext.lower()

            if ext_lower in self.text_formats:
                text_files.append(file_path)
            elif ext_lower in self.image_formats:
                image_files.append(file_path)
            else:
                logger.warning(f"Unsupported file format: {file_path}")

        logger.info(f"Classified files: {len(text_files)} text, {len(image_files)} image")
        return text_files, image_files

    def classify_directory(self, source_dir: str) -> Tuple[List[str], List[str]]:
        """分类目录中的文件"""
        if not os.path.exists(source_dir):
            logger.error(f"Source directory not found: {source_dir}")
            return [], []

        all_files = []
        for root, _, files in os.walk(source_dir):
            for file in files:
                all_files.append(os.path.join(root, file))

        return self.classify_files(all_files)

    def process_text_files(self, text_files: List[str]) -> List[str]:
        """处理文本文件"""
        processed_files = []

        try:
            # 导入文本预处理器
            from src.ai_workflow.preprocess.text_preprocessor.text_preprocessor import TextProcessor
            text_processor = TextProcessor()

            for text_file in text_files:
                try:
                    logger.info(f"Processing text file: {text_file}")

                    # 读取文本内容
                    with open(text_file, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 处理文本（这里可以调用具体的文本预处理逻辑）
                    # 简化版本：直接拷贝到输出目录
                    filename = os.path.basename(text_file)
                    output_path = os.path.join(self.text_output_dir, filename)

                    with open(output_path, 'w', encoding='utf-8') as f:
                        f.write(content)

                    processed_files.append(output_path)
                    logger.info(f"Text file processed: {output_path}")

                except Exception as e:
                    logger.error(f"Failed to process text file {text_file}: {e}")

        except ImportError:
            logger.warning("Text preprocessor not available, skipping text processing")

        return processed_files

    def process_image_files(self, image_files: List[str]) -> List[str]:
        """处理图像文件"""
        return self.image_preprocessor.process_images(image_files)

    def process_materials(self, material_paths: List[str]) -> Dict[str, List[str]]:
        """批量处理素材"""
        logger.info(f"Processing {len(material_paths)} materials")

        # 分类文件
        text_files, image_files = self.classify_files(material_paths)

        # 处理结果
        results = {
            "text_files": [],
            "image_files": []
        }

        # 处理文本文件
        if text_files:
            logger.info(f"Processing {len(text_files)} text files")
            results["text_files"] = self.process_text_files(text_files)

        # 处理图像文件
        if image_files:
            logger.info(f"Processing {len(image_files)} image files")
            results["image_files"] = self.process_image_files(image_files)

        logger.info(f"Processing completed: {len(results['text_files'])} text, {len(results['image_files'])} image")
        return results

    def process_directory(self, source_dir: str) -> Dict[str, List[str]]:
        """处理目录中的所有素材"""
        logger.info(f"Processing directory: {source_dir}")

        # 分类目录中的文件
        text_files, image_files = self.classify_directory(source_dir)

        # 处理结果
        results = {
            "text_files": [],
            "image_files": []
        }

        # 处理文本文件
        if text_files:
            results["text_files"] = self.process_text_files(text_files)

        # 处理图像文件
        if image_files:
            results["image_files"] = self.process_image_files(image_files)

        return results

    def get_output_info(self) -> Dict[str, str]:
        """获取输出目录信息"""
        return {
            "output_directory": self.output_dir,
            "text_output_directory": self.text_output_dir,
            "image_output_directory": self.image_output_dir,
            "image_input_directory": self.image_preprocessor.input_dir
        }
